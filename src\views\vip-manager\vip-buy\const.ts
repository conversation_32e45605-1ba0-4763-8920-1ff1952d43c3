/** VIP 购买类型  */
export enum VIPBuyType {
  /** 购买  */
  Buy = '1',
  /** 续费  */
  Renew = '2',
  /** 扩容  */
  Scaling = '3',
  /** 升级  */
  Upgrade = '4',
}

/** VIP 类型  */
export enum PackageType {
  /** 免费  */
  free = 0,
  /** VIP  */
  vip = 1
}

/** 支付方式 */
export enum PaymentTypes {
  /** 支付宝  */
  Ali = 1,
  /** 微信  */
  Wechat = 2,
  /** 余额  */
  Balance = 3,
  /** 聚合  */
  Aggregate = 4,
  /** 信用余额 */
  Credit = 23,
}
 
/** 支付步骤 */
export enum PayStep {
  /** 套餐选择  */
  Package = 1,
  /** 成功  */
  Success = 2,
}

/** 监管权益类型  */
export enum SuperviseType {
  /** 账号模式  */
  acc = 0,
  /** 用户模式  */
  member = 1
}


