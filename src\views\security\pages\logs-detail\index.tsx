import React, { useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';
import ClientRouter from '@/base/client/client-router';
import { useSearchParams } from 'react-router-dom';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils/to';
import _ from 'lodash';
import SwiperImages, { ImageItem, SwiperImagesRef } from '@/components/swiper-images';
import { OperateType } from '@/views/security/enum';
import BrowserSVG from '@/assets/browser.svg';
import { useLoading } from '@/hooks/loading';
import { useMemoizedFn } from 'ahooks';
import SuperEmpty from '@/components/super-empty';
import HeaderNavbar from '@/components/header-navbar';
import { AiOutlineMenu } from 'react-icons/ai';
import SuperPopup from '@/components/super-popup';
import BaseInfoLogsDetail from '@/views/security/pages/logs-detail/base-info';
import SearchFilter, { FilterConditions } from './search-filter';
import LogsSteps from './logs-steps';

const LogsDetail: React.FC = () => {
  const [searchParams] = useSearchParams();
  const clientRouter = ClientRouter.getRouter();
  const session_id = searchParams.get('session_id');
  const machine_string = searchParams.get('machine_string');
  const { startLoading, stopLoading } = useLoading();
  const [infoVisible, setInfoVisible] = useState(false);
  const img_count = useRef<number>(0);
  const [logGroups, setLogGroups] = useState<SuperviseModule.LogsDetailGroups[]>(
    [] as SuperviseModule.LogsDetailGroups[]
  );
  const [filterKeyword, setFilterKeyword] = useState('');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const swiperRef = useRef<SwiperImagesRef>(null);
  const [originData, setOriginData] = useState<SuperviseModule.LogsDetailResponse>();
  const [images, setImages] = useState<ImageItem[]>([]);
  const [currentIndex, setCurrentIndex] = useState(1);

  // 保存原始数据用于过滤
  const [originalLogGroups, setOriginalLogGroups] = useState<SuperviseModule.LogsDetailGroups[]>([]);
  const [originalImages, setOriginalImages] = useState<ImageItem[]>([]);

  // 持久化筛选条件状态
  const [currentFilterConditions, setCurrentFilterConditions] = useState<FilterConditions>({
    keyword: '',
    startTime: null,
    endTime: null,
    operateTypes: [],
  });

  const handleInputChange = useMemoizedFn((val) => {
    setFilterKeyword(val);

    // 更新筛选条件中的关键字，保持其他筛选条件不变
    const updatedConditions = {
      ...currentFilterConditions,
      keyword: val,
    };

    setCurrentFilterConditions(updatedConditions);
    // 实时搜索：使用更新后的完整筛选条件进行过滤
    filterData(updatedConditions);
  });

  // 过滤数据的函数
  const filterData = useMemoizedFn((conditions: FilterConditions) => {
    const { keyword, startTime, endTime, operateTypes } = conditions;

    // 如果没有任何筛选条件，返回原始数据
    if (!keyword && !startTime && !endTime && operateTypes.length === 0) {
      setImages(originalImages);
      setLogGroups(originalLogGroups);
      return;
    }

    // 过滤分组数据
    const filteredGroups = originalLogGroups.map(group => {
      // 检查分组名称或URL是否匹配关键字
      const groupMatches = keyword ?
        (group.name.toLowerCase().includes(keyword.toLowerCase()) ||
         group.url.toLowerCase().includes(keyword.toLowerCase())) : true;

      if (!groupMatches) {
        return null; // 分组不匹配，跳过
      }

      // 过滤分组内的事件
      const filteredEvents = group.events.filter(event => {
        // 时间筛选
        if (startTime && endTime) {
          const eventTime = event.create_time * 1000; // 转换为毫秒
          const start = startTime.getTime();
          const end = endTime.getTime();
          if (eventTime < start || eventTime > end) {
            return false;
          }
        }

        // 操作行为筛选
        if (operateTypes.length > 0) {
          if (!operateTypes.includes(event.type)) {
            return false;
          }
        }

        return true;
      });

      // 如果过滤后没有事件，返回null
      if (filteredEvents.length === 0) {
        return null;
      }

      return {
        ...group,
        events: filteredEvents
      };
    }).filter(Boolean) as SuperviseModule.LogsDetailGroups[];

    // 重新生成图片数组和索引
    img_count.current = 0;
    const filteredImages: ImageItem[] = _.flatMap(filteredGroups, (group) =>
      group.events.map((item) => {
        const img = Number(item.type) === OperateType.Access ? BrowserSVG : item.image_url;
        return {
          event_id: item.event_id,
          src: img,
          alt: item.title,
        };
      })
    );

    // 重新关联索引
    const concat_groups = filteredGroups.map((item) => {
      item.events = item.events.map((event) => {
        event.img_index = img_count.current + 1;
        img_count.current += 1;
        return event;
      });
      return item;
    });

    setImages(filteredImages);
    setLogGroups(concat_groups);
    setCurrentIndex(1); // 重置到第一张图片
  });

  // 处理步骤点击 - 跳转到指定的轮播图索引
  const handleStepClick = useMemoizedFn((imgIndex: number) => {
    if (swiperRef.current && imgIndex > 0) {
      // 将 img_index (从1开始) 转换为 swiper index (从0开始)
      const swiperIndex = imgIndex - 1;
      swiperRef.current.swipeTo(swiperIndex);
      setCurrentIndex(imgIndex);
    }
  });

  // 暂停轮播自动播放
  const handlePauseAutoplay = useMemoizedFn(() => {
    if (swiperRef.current) {
      swiperRef.current.stopAutoplay();
    }
  });
  const getLogDetail = async () => {
    if (session_id) {
      const [err, res] = await to<SuperviseModule.LogsDetailResponse>(
        superviseService.superviseLogDetail({ session_id })
      );
      stopLoading();
      if (err) return;
      setOriginData(res);

      // 重置图片计数器
      img_count.current = 0;

      const imgs: ImageItem[] = _.flatMap(res.groups, (group) =>
        group.events.map((item) => {
          const img = Number(item.type) === OperateType.Access ? BrowserSVG : item.image_url;
          return {
            event_id: item.event_id,
            src: img,
            alt: item.title,
          };
        })
      );
      /**关联索引 */
      const concat_groups = res.groups.map((item) => {
        item.events = item.events.map((event) => {
          event.img_index = img_count.current + 1;
          img_count.current += 1;
          return event;
        });
        return item;
      });
      // 保存原始数据
      setOriginalImages(imgs);
      setOriginalLogGroups(concat_groups);

      // 设置当前显示的数据
      setImages(imgs);
      setLogGroups(concat_groups);

      // 如果有当前的筛选条件，立即应用
      if (currentFilterConditions.keyword ||
          currentFilterConditions.startTime ||
          currentFilterConditions.endTime ||
          currentFilterConditions.operateTypes.length > 0) {
        // 延迟应用筛选，确保状态已更新
        setTimeout(() => {
          filterData(currentFilterConditions);
        }, 0);
      }
    }
  };
  useEffect(() => {
    startLoading();
    getLogDetail();
  }, []);

  // 当原始数据变化时，重新应用当前的筛选条件
  useEffect(() => {
    if (originalLogGroups.length > 0) {
      filterData(currentFilterConditions);
    }
  }, [originalLogGroups, currentFilterConditions, filterData]);

  console.log('@@@@originData', originData);
  console.log('@@@@logGroups', logGroups);
  console.log('@@@@imgs', images);
  console.log('@@@onChange', currentIndex);

  return (
    <div className={styles.logsDetail}>
      <HeaderNavbar
        onBack={() => clientRouter.goBack()}
        title="日志详情"
        rightNode={<AiOutlineMenu size={24} onClick={() => setInfoVisible(true)} />}
      />
      {images.length === 0 ? (
        <SuperEmpty style={{ height: '200px' }} />
      ) : (
        <SwiperImages
          ref={swiperRef}
          images={images}
          height="200px"
          autoplay={true}
          showToolbar={true}
          showRotateButton={true}
          showScaleButtons={true}
          showFullscreenButton={true}
          showLandscapeButton={true}
          showViewerPlayButton={true}
          viewerAutoplayInterval={2000}
          autoplayInterval={2000}
          viewerAutoplayLoop={true}
          pauseViewerOnInteraction={true}
          className={styles.customSwiper}
          onChange={(index) => {
            setCurrentIndex(index + 1);
          }}
        />
      )}
      <div className={styles.searchBox}>
        <SearchFilter
          filterKeyword={filterKeyword}
          handleInputChange={handleInputChange}
          drawerOpen={drawerOpen}
          setDrawerOpen={setDrawerOpen}
          currentConditions={currentFilterConditions}
          onFilter={(conditions) => {
            // 持久化筛选条件
            setCurrentFilterConditions(conditions);
            // 应用筛选
            filterData(conditions);
          }}
          onReset={() => {
            // 重置所有筛选条件
            const resetConditions: FilterConditions = {
              keyword: '',
              startTime: null,
              endTime: null,
              operateTypes: [],
            };
            setCurrentFilterConditions(resetConditions);
            setFilterKeyword('');
            filterData(resetConditions);
          }}
        />
      </div>
      <div className={styles.content}>
        <LogsSteps
          currentIndex={currentIndex}
          data={logGroups}
          onStepClick={handleStepClick}
          onPauseAutoplay={handlePauseAutoplay}
        />
      </div>
      <SuperPopup title="基础信息" visible={infoVisible} onClose={() => setInfoVisible(false)}>
        <BaseInfoLogsDetail originData={originData!} machine_string={machine_string!} />
      </SuperPopup>
    </div>
  );
};

export default observer(LogsDetail);
