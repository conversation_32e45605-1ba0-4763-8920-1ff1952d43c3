import { computed, makeObservable, when } from 'mobx';
import {
  clientConfig,
  type LoginService,
  type LoginServicePayload,
  to,
  type IClientResponse,
} from '@ziniao-fe/core';
import { Modules } from '@/websocket';
import { Logs } from '@/utils';
import configs from '@/configs';
import WS from './ws';
import { urlTool } from '@/utils';
import { nanoid } from 'nanoid';
import ClientRouter from '@/base/client/client-router';
import type AndroidRouter from '@/base/client/android/android-router';
import { tools } from '@/utils/tools';
import { vConsole } from '@/utils/logs';

type IMessageListener = (...args: any[]) => any;

class AndroidSdk {
  public clientRouter: AndroidRouter;
  static CLIENT_NAME = 'SBAndroid';

  /**
   * websocket对象
   * @description 组合替代继承
   */
  private ws = new WS(AndroidSdk.CLIENT_NAME + `${nanoid()}`);

  constructor() {
    makeObservable(this, {
      connected: computed,
    });
    this.clientRouter = ClientRouter.getRouter(this.ws) as AndroidRouter;
  }

  /**
   * 已经成功连接
   * @computed
   */
  get connected() {
    return !!this.ws.connected;
  }

  /** 调用客户端提供能力 */
  invoke = async <T = any>(module: string, action: string, params?: any) => {
    await when(() => !!this.connected);

    const actionKey = clientConfig.getActionKey(module, action);
    const sendData = clientConfig.formatSendData(module, action, params);
    Logs.warn(`-------- websocket ${actionKey} client request -----`);
    Logs.log(sendData);

    const [err, resultData] = await to(this.ws.superWebsocket?.request<T>(module, action, params)!);
    if (err) return;

    if (module !== 'LogModule') {
      // 客户端的打印日志暂时不显示在console窗口内
      Logs.warn(
        `-------- ${clientConfig.supportIPC ? 'IPC' : 'websocket'} result ${actionKey} -----`
      );
      Logs.log(resultData);
    }

    if (resultData!.ret === 0) {
      return Promise.resolve(resultData!.returnObj);
    } else {
      // @ts-ignore
      let message = resultData?.returnObj?.message ?? resultData?.returnObj?.msg ?? '';
      try {
        message = JSON.stringify(resultData);
      } catch (e) {
        message = '"Network error, please check your network and try again"';
      }
      return Promise.reject(new Error(message));
    }
  };

  /** 注册广播回调 */
  registerBroadcast = (module: string, action: string, listener: IMessageListener) => {
    return this.ws.superWebsocket?.addListener(module, action, (result: IClientResponse) => {
      if (!clientConfig?.isIgnoreAction(result?.action)) {
        console.warn(`Receive Websocket message: ${result?.module}/${result?.action}`);
        Logs.log(result);
      }

      listener(result);
    });
  };

  /** 移除广播回调 */
  removeBroadcast = (module: string, action: string, listener: IMessageListener) => {
    Logs.log('Remove Websocket broadcast', `${module}/${action}`);

    return this.ws.superWebsocket?.removeListener(module, action, listener);
  };

  /**
   * 获取位数
   */
  getBitVersion = async () => {
    let res = await this.invoke(
      Modules.SystemInfoModule.key,
      Modules.SystemInfoModule.action.GetCurrentVersion
    );
    return res as string;
  };
  /**
   * 获取当前版本号
   */
  getVersion = async () => {
    let res = await this.invoke(
      Modules.SystemInfoModule.key,
      Modules.SystemInfoModule.action.GetVersion
    );

    return res as string;
  };

  getPublicProperties = async () => {
    try {
      let res = await this.invoke(
        Modules.AnalysisModule.key,
        Modules.AnalysisModule.action.GetProperties
      );

      return res;
    } catch (e) {
      return null;
    }
  };
  buriedPoint = async (eventName: string, params: Record<string, any>) => {
    try {
      let res = await this.invoke(
        Modules.AnalysisModule.key,
        Modules.AnalysisModule.action.RecordCountEvent,
        [eventName, params]
      );

      return res;
    } catch (e) {
      return null;
    }
  };
  getAPILines = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.GetNetLineInfo
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /** 设置线路 */
  setAPILines = async (currentKey) => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.SetSelectNetLineKey,
        [currentKey]
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /** 获取摄像头选项 */
  getCameraIndex = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.GetWebCamIndex
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /** 设置摄像头选项 */
  setCameraIndex = async (index) => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.SetWebCamIndex,
        [index]
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /** 复制日志 */
  copyLogFilePath = async () => {
    try {
      let res = await this.invoke(Modules.LogModule.key, Modules.LogModule.action.CopyLogFilePath);
      return res;
    } catch (e) {
      return null;
    }
  };
  /**
   * 获取当前是否是有新版本可更新
   */
  haveNewVersion = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.HaveNewVersion
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /**
   * 获取当前是否是有新版本可更新
   */
  getCoreVersion = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.GetCoreVersion
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /**
   * 获取当前是否是有新版本可更新
   */
  GetCurrentVersion = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.GetCurrentVersion
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /**
   * 版本检测
   */
  checkNewVersion = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.CheckNewVersion
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /**
   * 获取当前版本是测试版本或者是正式版本, 测试版本会在版本号旁边加beta
   */
  getReleasePhase = async () => {
    let res = await this.invoke(
      Modules.SystemInfoModule.key,
      Modules.SystemInfoModule.action.GetReleasePhase
    );

    return res as number;
  };
  /**
   * 消息推送
   */
  setPushMessageConfig = async (type, opened) => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.SetPushMessageConfig,
        [type, opened]
      );
      return res;
    } catch (e) {
      Logs.error(e);
      return null;
    }
  };
  /**
   * 获取消息列表
   */
  getPushMessageList = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.GetPushMessageList
      );
      return res;
    } catch (e) {
      Logs.error(e);
      return null;
    }
  };
  /**
   * 获取机器码信息
   */
  getMachineInfo = async () => {
    try {
      let machineData = await this.invoke<AndroidClient.MachineInfo>(
        Modules.AuthoModule.key,
        Modules.AuthoModule.action.GetMachineInfo
      );

      // const macAddresses = await this.getMacString();

      const result: AndroidClient.MachineInfo = machineData!;
      const info: SuperClient.MachineInfoData = {
        clientEncodeCode: result.pan_gu_epoch,
        macAddresses: [],
        machineName: result.machine_name,
        machineCode: result.machine_string,
        machineCodeNew: result.machine_string_new,
      };

      return info;
    } catch (e) {}
  };

  /**
   * 获取mac地址
   * @description 安卓中没有该方法
   * @deprecated
   */
  getMacString = async () => {
    let res;
    try {
      res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.GetMacs
      );
      return JSON.parse(res) as string[];
    } catch (error) {
      return [];
    }
  };

  getClientConfig = async (): Promise<SuperClient.Config> => {
    const envid = this.ws.initData!.envid as SuperClient.Config['env'];
    // 运行时前端默认配置
    const runtimeConf = configs[envid ?? 'production'];
    const [err, response] = await to(this.getApiPreUrlInfo());
    const commonConf = {
      env: envid,
      fadadaUrl: runtimeConf.fadadaUrl,
      officialWebsite: runtimeConf.official,
      /**
       * 远程地址目前对安卓没用，先统一返回
       */
      webRemoteUrl: runtimeConf.webRemoteUrl,
      adminPage: '',
    };
    // 获取客户端失败则直接用前端的配置
    if (err) {
      return {
        ...commonConf,
        sems: runtimeConf.host,
        ssos: runtimeConf.SSOS,
      };
    }
    try {
      if (envid === 'production') {
        const vconsole = await this.getStorage('vconsole');
        vconsole ? vConsole.showSwitch() : vConsole.hideSwitch();
      } else {
        vConsole.showSwitch();
      }
    } catch (error) {
      console.log(error);
    }

    const data: AndroidClient.ServiceBaseURLConfig = JSON.parse(response);
    return {
      ...commonConf,
      sems: data.SEMS,
      ssos: data.SSOS,
    };
  };

  /**
   * @description 客户端转发http请求，可以优化用户网络线路
   */
  requestHttp = (requestConfig: AndroidClient.OfflineRequestConfig) => {
    return this.invoke<AndroidClient.OfflineResponse>(
      Modules.ServerAPIModule.key,
      Modules.ServerAPIModule.action.RequestHttp,
      [requestConfig]
    );
  };

  getStorage = async <T = any>(key: string, session = false): Promise<null | T> => {
    const [err, response] = await to(
      this.invoke(
        Modules.StorageModule.key,
        session
          ? Modules.StorageModule.action.GetSessionStorage
          : Modules.StorageModule.action.GetLocalStorage,
        key
      )
    );
    if (err) return null;

    try {
      const result = JSON.parse(response || 'null');
      const expireTime = result?.expireTime;
      const data = result?.data;
      const now = new Date().getTime();
      // 过期自动处理
      if (expireTime && now > expireTime) {
        this.setStorage(key, null); // 过期置空

        return null;
      }

      return data;
    } catch (error) {
      return null;
    }
  };

  setStorage = async (
    key: string,
    value: any,
    options: {
      session?: boolean;
      expireTime?: null | number;
    } = {
      session: false,
      expireTime: null,
    }
  ) => {
    // 可序列化的值
    const stringify = value ?? null;

    try {
      const message = JSON.stringify({
        data: stringify,
        expireTime: options.expireTime,
      });

      const [err, response] = await to(
        this.invoke(
          Modules.StorageModule.key,
          !!options?.session
            ? Modules.StorageModule.action.SetSessionStorage
            : Modules.StorageModule.action.SetLocalStorage,
          [key, message]
        )
      );

      if (err) throw err;

      // if (response?.ret !== 0)
      //   throw new Error(`Write client storage failed, key: ${key}, value: ${stringify}`);

      return;
    } catch (error) {
      throw error;
    }
  };

  /**生产返回对象为空，需要注意 */

  getApiPreUrlInfo = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.GetApiPreUrlInfo
      );
      return res;
    } catch (e) {
      throw e;
    }
  };
  setCustomApiPreUrl = async (name, value) => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.SetCustomApiPreUrl,
        [name, value]
      );

      return res;
    } catch (e) {}
  };
  getLoginInfo = async () => {
    try {
      let res = await this.invoke(Modules.AuthoModule.key, Modules.AuthoModule.action.GetLoginInfo);
      const data = tools.safeParseJSON(res);
      return data;
    } catch (e) {}
  };

  login = async (params: LoginServicePayload.LoginParams) => {
    const [err, response] = await to(
      this.invoke(Modules.AuthoModule.key, Modules.AuthoModule.action.Login, params)
    );
    if (err) return Promise.reject(err);

    return response as Promise<AndroidClient.LoginResponse>;
  };

  logout = async (_params: SuperClient.LogoutParams, options?: SuperClient.LogoutOptions) => {
    try {
      const params = {
        language: 0,
        erp_mode: '',
        user_id: _params.userId,
        company_id: _params.companyId,
        machine_string: _params.machineString,
        special_key: true,
        is_jump_toLogin: options?.backLoginView ?? true,
      };

      let userInfo = await this.invoke(Modules.AuthoModule.key, Modules.AuthoModule.action.Logout, [
        params,
        options?.manual ?? false,
      ]);

      return userInfo;
    } catch (e) {}
  };

  onUserChange = (callback: (loginInfo: LoginService.LoginUserInfo | null) => void) => {
    this.registerBroadcast(
      Modules.AuthoModule.key,
      Modules.AuthoModule.action.OnLogin,
      async (result) => {
        const user = result?.args;
        // 直接用广播，不需要旧逻辑另外取
        // const user = await this.getLoginInfo();
        callback(user);
      }
    );

    this.registerBroadcast(Modules.AuthoModule.key, Modules.AuthoModule.action.OnLogout, () => {
      callback(null);
    });
  };

  /**
   * 设置Tabbar
   * @description 多webview，每个tabbar是一个webview，tabbar切换是webview层切换
   */
  setTabBar = async (tabOptions: AndroidClient.TabBarOption[]) => {
    try {
      const result = await this.invoke('MainViewSettingModule', 'SetTabBar', [tabOptions]);

      return result;
    } catch (e) {
      return null;
    }
  };
  /**
   * 设置Tabbar
   * @description
   */
  getTabBar = async () => {
    try {
      const result = await this.invoke('MainViewSettingModule', 'GetTabBar');

      return result;
    } catch (e) {
      return null;
    }
  };
  SetTabBarBadge = async (index: number, text?: string) => {
    try {
      const result = await this.invoke('MainViewSettingModule', 'SetTabBarBadge', [index, text]);

      return result;
    } catch (e) {
      return null;
    }
  };
  SetTabBarIndex = async (index: number) => {
    try {
      const result = await this.invoke('MainViewSettingModule', 'SetTabBarIndex', index);

      return result;
    } catch (e) {
      return null;
    }
  };
  GetTabBarIndex = async () => {
    try {
      const result = await this.invoke('MainViewSettingModule', 'GetTabBarIndex');

      return result;
    } catch (e) {
      return null;
    }
  };

  /**
   * 唤起客户端支付宝支付
   * @param url
   */
  callClientAliPay = async (url: string): Promise<{ isSuccess: boolean; msg: string }> => {
    let res = {
      isSuccess: false,
      msg: '支付失败',
    };
    try {
      let urlObj = new URL(url);
      let callPayResult = await this.invoke(
        Modules.PayModule.key,
        Modules.PayModule.action.AliPay,
        [urlObj.search.split('?')[1]]
      );
      if (callPayResult?.ret == 0) {
        res.isSuccess = true;
      } else if (!!callPayResult?.msg) {
        res.msg = callPayResult?.msg;
      }
    } catch (error) {}
    return res;
  };

  /**
   * 唤起客户端微信支付
   * @param payConfig
   */
  callClientWechatPay = async (payConfig): Promise<{ isSuccess: boolean; msg: string }> => {
    let res = {
      isSuccess: false,
      msg: '支付失败',
    };
    try {
      let callPayResult = await this.invoke(
        Modules.PayModule.key,
        Modules.PayModule.action.WeChatPay,
        [payConfig]
      );
      if (callPayResult?.ret == 0) {
        res.isSuccess = true;
      } else if (!!callPayResult?.msg) {
        res.msg = callPayResult?.msg;
      }
    } catch (error) {}
    return res;
  };
  /**获取当前模式 */
  getPageDisplayType = async () => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.GetPageDisplayType
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /**获取当前模式 */
  setPageDisplayType = async (value) => {
    try {
      let res = await this.invoke(
        Modules.SystemInfoModule.key,
        Modules.SystemInfoModule.action.SetPageDisplayType,
        value
      );
      return res;
    } catch (e) {
      return null;
    }
  };
  /** 切换公司 - 客户端显示loading */
  showSwitchLoading = async (value: {
    company_name: string;
    user_id: number;
    company_id: number;
    token: string;
  }) => {
    const params = {
      company_name: value.company_name,
      user_id_switch: value.user_id,
      company_id_switch: value.company_id,
      token: value.token,
    };
    try {
      await this.invoke(Modules.AuthoModule.key, Modules.AuthoModule.action.ShowSwitchLoading, [
        params,
      ]);
    } catch (error) {
      return null;
    }
  };
  /** 切换公司 - 客户端关闭loading */
  closeSwitchLoading = async () => {
    try {
      await this.invoke(Modules.AuthoModule.key, Modules.AuthoModule.action.CloseSwitchLoading);
    } catch (error) {
      return null;
    }
  };
  /** 切换公司 - 客户端前往登录页 */
  goToLogin = async () => {
    try {
      await this.invoke(Modules.AuthoModule.key, Modules.AuthoModule.action.GotoLogin);
    } catch (error) {
      return null;
    }
  };
  LongTouch = (data) => {
    try {
      this.invoke(Modules.SystemInfoModule.key, Modules.SystemInfoModule.action.LongTouch, data);
    } catch (error) {
      return null;
    }
  };
}

export default AndroidSdk;
