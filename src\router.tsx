import _ from 'lodash';
import { createHashRouter, Navigate } from 'react-router-dom';
import React, { lazy } from 'react';
import {
  ACCOUNT_DETAIL_ACTION,
  WORKBENCH_URL,
  APP_ROUTER,
  USER_ROUNTER,
  SETTING_ROUNTER,
} from './constants';
import securityRouter from './routers/security';
const LoginView = lazy(() => import('@/views/login'));
const ErrorPage = lazy(() => import('./error-page'));
const NotFound = lazy(() => import('./not-found'));
const MainPage = lazy(() => import('@/views'));
const Todo = lazy(() => import('@/views/todo'));
const ShopList = lazy(() => import('@/views/shop-list'));
const RemoteRecord = lazy(() => import('@/views/shop-list/components/remote/record'));
const IOSPayApp = lazy(() => import('@/views/ios-pay-app'));
const Notice = lazy(() => import('@/views/notice'));
const NoticeDetail = lazy(() => import('@/views/notice/components/detail'));
const MemberAccessList = lazy(() => import('@/views/member-access/list'));
// const MemberJoinDetail = lazy(() => import('@/views/member-join/detail'));
const MemberJoinList = lazy(() => import('@/views/member-join/list'));
const MemberLoginDetail = lazy(() => import('@/views/member-login/detail'));
const MemberLoginList = lazy(() => import('@/views/member-login/list'));
const ExpiringDevicesList = lazy(() => import('@/views/expiring-devices/list'));
// const ExpiringDevicesDetail = lazy(() => import('@/views/expiring-devices/detail'));
const ExpiringDevicesRenew = lazy(() => import('@/views/expiring-devices/renew'));
const ExpiringCloudList = lazy(() => import('@/views/expiring-cloud/list'));
const ExpiringCloudDetail = lazy(() => import('@/views/expiring-cloud/detail'));
const UnpaidOrder = lazy(() => import('@/views/unpaid-order'));
const Recharge = lazy(() => import('@/views/recharge'));
const BankTransfer = lazy(() => import('@/views/recharge/bank-transfer'));
const UserCenter = lazy(() => import('@/views/user-center'));
const Help = lazy(() => import('@/views/help'));
const Coupons = lazy(() => import('@/views/coupons'));
const AboutMe = lazy(() => import('@/views/setting/about-me'));
const AccountDetail = lazy(() => import('@/views/account-detail'));
const Setting = lazy(() => import('@/views/setting'));
const MessagePush = lazy(() => import('@/views/setting/message-push'));
const RemoteIos = lazy(() => import('@/views/remote-ios'));
const EnvSetting = lazy(() => import('@/views/setting/env'));
const SHOP_AUTH = lazy(() => import('@/views/shop-auth'));
const SwitchAwait = lazy(() => import('@/views/switch-await'));
const ProxyRouter = lazy(() => import('@/views/proxy-router'));
const IosTest = lazy(() => import('@/views/ios-test'));
const WarningDetail = lazy(() => import('@/views/warning-detail'));
const VipBuy =  lazy(() => import('@/views/vip-manager/vip-buy'));
const VipBuyServiseProtocol =  lazy(() => import('@/views/vip-buy-servise-protocol'));

import { fetchAccountDetail } from '@/views/account-detail';

const router = createHashRouter([
  {
    path: WORKBENCH_URL.LOGIN,
    element: <LoginView />,
  },
  {
    path: 'ios',
    element: <IosTest />,
  },
  {
    path: SETTING_ROUNTER.SETTING,
    element: <Setting />,
  },
  {
    path: SETTING_ROUNTER.ABOUT,
    element: <AboutMe />,
  },
  {
    path: SETTING_ROUNTER.ENVSETTING,
    element: <EnvSetting />,
  },
  // 企业切换
  {
    path: WORKBENCH_URL.SWITCH,
    element: <SwitchAwait />,
  },
  {
    path: APP_ROUTER.SHOP_AUTH,
    element: <SHOP_AUTH />,
  },
  {
    path: USER_ROUNTER.USER_NOTICE_DETAIL + '/:id',
    // element: <ProxyRouter />,
    element: <Navigate to={USER_ROUNTER.NOTICE} replace />,
  },
  {
    path: WORKBENCH_URL.SHOP_LIST,
    element: <ShopList />,
  },
  {
    path: APP_ROUTER.CLOUD_APPLICATION,
    element: <RemoteIos />,
  },
  {
    path: APP_ROUTER.HOME,
    element: <MainPage />,
    errorElement: <ErrorPage />,
    children: [
      {
        path: USER_ROUNTER.TODO,
        element: <Todo />,
      },

      {
        path: APP_ROUTER.REMOTE_RECORD,
        element: <RemoteRecord />,
      },
      {
        path: APP_ROUTER.REMOTE_PAY_APP,
        element: <IOSPayApp />,
      },
      {
        path: USER_ROUNTER.NOTICE,
        element: <Notice />,
      },
      {
        path: USER_ROUNTER.NOTICE_DETAIL,
        element: <NoticeDetail />,
      },
      {
        path: APP_ROUTER.MEMBER_ACCESS,
        element: <MemberAccessList />,
      },
      // {
      //   path: APP_ROUTER.MEMBER_JOIN_DETAIL,
      //   element: <MemberJoinDetail />,
      // },
      {
        path: APP_ROUTER.MEMBER_JOIN_LIST,
        element: <MemberJoinList />,
      },
      {
        path: APP_ROUTER.MEMBER_LOGIN_DETAIL,
        element: <MemberLoginDetail />,
      },
      {
        path: APP_ROUTER.MEMBER_LOGIN_LIST,
        element: <MemberLoginList />,
      },
      {
        path: APP_ROUTER.EXPIRING_DEVICES_LIST,
        element: <ExpiringDevicesList />,
      },
      {
        path: APP_ROUTER.EXPIRING_DEVICES_RENEW,
        element: <ExpiringDevicesRenew />,
      },
      {
        path: APP_ROUTER.EXPIRING_CLOUD_LIST,
        element: <ExpiringCloudList />,
      },
      {
        path: APP_ROUTER.EXPIRING_CLOUD_DETAIL,
        element: <ExpiringCloudDetail />,
      },
      {
        path: APP_ROUTER.UNPAID_ORDER,
        element: <UnpaidOrder />,
      },
      {
        path: APP_ROUTER.RECHARGE,
        element: <Recharge />,
      },
      {
        path: APP_ROUTER.BANK_TRANSFER,
        element: <BankTransfer />,
      },
      {
        path: USER_ROUNTER.USER,
        element: <UserCenter />,
      },
      {
        path: USER_ROUNTER.HELP,
        element: <Help />,
      },
      {
        path: USER_ROUNTER.COUPONS,
        element: <Coupons />,
      },
      {
        path: APP_ROUTER.WARNING_DETAIL,
        element: <WarningDetail />,
      },
      {
        path: `${WORKBENCH_URL.ACCOUNT}/:id`,
        element: <AccountDetail />,
        loader: async ({ params, request }) => {
          const url = new URL(request.url);
          const categoryId = url.searchParams.get(ACCOUNT_DETAIL_ACTION.categoryId) || '';
          return fetchAccountDetail(params?.id!, categoryId);
        },
      },
      ...securityRouter,
      {
        path: SETTING_ROUNTER.MESSAGE_PUSH,
        element: <MessagePush />,
      },
      {
        path: APP_ROUTER.VIP_BUY,
        element: <VipBuy />,
      },
      {
        path: APP_ROUTER.VIP_BUY_SERVISE_PROTOCOL,
        element: <VipBuyServiseProtocol />,
      },
      {
        path: '*',
        element: <NotFound />,
      },
    ],
  },
]);

export default router;
