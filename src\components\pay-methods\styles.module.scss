.radioItem {
  flex-direction: row-reverse;
  justify-content: space-between;
  min-height: 46px;
  box-sizing: border-box;

  &+.radioItem {
    padding: 10px 0;
  }

  :global {
    .adm-radio-content {
      padding-left: 0;
    }
  }
}

.payItem {
  display: flex;
  align-items: center;

  .icon {
    color: #fff;
    width: 24px;
    height: 24px;
    text-align: center;
    border-radius: 10%;
    margin-right: 10px;

    &.balance {
      width: 5.4vw;
      height: 5.4vw;
      padding: 1vw;
      background: linear-gradient(135deg, #ffa00f 0%, #fc620b 100%);
    }

    &.ali {
      background-color: $white;
      color: #1677ff;
    }

    &.wx {
      background: #24b340;
    }
  }

  .payName {
    font-size: $font-size-base;
    line-height: 21px;

    .num {
      font-size: $font-size-small;
      color: $color-text-tertiary;
      line-height: 18px;
    }

    .recharge {
      margin-left: 10px;
      color: $color-primary-text;
    }

    .payDesc {
      font-size: $font-size-small;
      color: $color-text-tertiary;
      line-height: 16px;
      margin-top: 2px;
    }
  }
}

.checkedStyle {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  width: 20px;
  height: 20px;
  background: #FFFFFF;
  border-radius: 50%;
  border: 1.5px solid #3569FD;

  &::before {
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #3569FD;
  }
}
.refresh{
  color: #3569FD;
}