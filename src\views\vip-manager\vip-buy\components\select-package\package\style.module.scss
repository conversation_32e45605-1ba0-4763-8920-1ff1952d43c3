.package {
  background: white;
  padding: 12px;
  width: calc(100% - 48px);
  overflow: auto;
  border-radius: 4px;
  .card {
    background: linear-gradient(90deg, #FBDEB4 0%, #FFF7E7 99%);
    width: 153px;
    height: 132px;
    display: flex;
    align-items: center;
    flex-direction: column;
    border-radius: 6px;
    border: 2px solid #D09E4D;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    .vipImg {
      width: 150px;
      height: 132px;
      position: absolute;
      bottom: -49px;
      right: -47px;
    }

    .discountTag {
      position: absolute;
      padding: 0px 5px;
      background: $color-danger;
      border-radius: 2px;
      color: white;
      right: 0px;
      top: 0px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      z-index: 2;
    }

    .days {
      color: #7C350D;
      position: relative;
      // top: 4px;
      // left: 2px;
      font-size: 16px;
      margin-top: 24px;
      z-index: 2;
    }

    .priceWrp {
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-top: 8px;
      z-index: 2;

      .priceText {
        font-size: 14px;
        color: #7C350D;

        .price {
          font-family: PingFang SC, PingFang SC;
          font-size: 30px;
          color: #7C350D;
          font-weight: bold;
        }
      }
    }

    .discountWrp {
      // display: flex;
      // flex: 1;
      // align-items: end;
      // width: 100%;
      z-index: 8;

      .discount {
        height: 32px;
        width: 100%;
        // background: #FFD18C;
        align-items: center;
        display: flex;
        justify-content: center;
        font-family: PingFang SC, PingFang SC;
        font-size: 12px;
        text-decoration-line: line-through;
        color: #7C350D;
        display: flex;
        align-items: center;
      }
    }
  }

  .noSelected {
    background: #FFFBF3;
    border: 2px solid rgba(208, 158, 77, 0.3);

    .days {
      color: $color-text-secondary;
    }

    .priceWrp {
      .priceText {
        color: $color-text;

        .price {
          color: $color-text;
        }
      }
    }

    .discountWrp {

      .discount {
        color: $color-text-tertiary;
        background: rgba(0, 0, 0, 0.02);

      }
    }
  }
}
