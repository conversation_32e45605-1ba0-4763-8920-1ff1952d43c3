import dayjs from 'dayjs';
import { SuperviseType, VIPBuyType } from './const';
import { add, multiply, divide } from 'mathjs';

export const toFixed = (number: number, precision = 100) => {
  return Math.round(number * precision) / precision;
};

export interface ComputeTotalPriceType {
  /** 购买类型 */
  vipBuyType?: VIPBuyType;
  /** 基础价格 */
  basePrice: number;
  /** 成员数量 */
  memberNums: number;
  /** 成员单价 */
  memberUnitPrice: number;
  /** 平台账号数量 */
  accNums: number;
  /** 平台账号单价 */
  accUnitPrice: number;

  /** 滚动存储时间价格单价 */
  rollingStorageUnitPrice: number;
  /** 滚动存储时间数量 */
  rollingStorageNums: number;
  /** 滚动存储时间步进数 */
  rollingStorageStep: number;
  /** 选中七天滚动存储 */
  rollingStorageIsSelected?: boolean;
  /** 过期时间 */
  expiry?: number;
  /** 当前时间 */
  serverTime?: number;
  /** 当前时间 */
  superviseType?: SuperviseType;
  /** 添加的名额倍数 */
  addSeatMultiple: number;
}

/** 计算扩容总金额 */
export const computeScalingTotalPrice = ({
  unitPrice,
  nums,
  expiry,
  serverTime
}: {
  unitPrice: number;
  nums: number;
  expiry?: number;
  serverTime?: number;
}) => {

  const diffInDays = Math.ceil(
    (dayjs(expiry).unix() - dayjs(serverTime || undefined).unix()) / (24 * 60 * 60),
  );
  const price = multiply(nums, unitPrice);

  const scalingPrice = toFixed(multiply(divide(price, 30), diffInDays));
  return scalingPrice;
};

/** 计算单个总金额 */
export const computeSinglePrice = ({
  unitPrice,
  nums,
  type,
  expiry,
  serverTime,
  addSeatMultiple
}: {
  unitPrice: number;
  nums: number;
  type?: VIPBuyType;
  expiry?: number;
  serverTime?: number;
  addSeatMultiple: number;
}) => {

  if (type === VIPBuyType.Scaling) {
    const price = computeScalingTotalPrice({ unitPrice: unitPrice, nums: nums, expiry, serverTime });
    return price;
  }

  return toFixed(multiply(unitPrice, nums * addSeatMultiple));
};

/** 获取总金额 */
export const computeTotalPrice = ({
  basePrice,
  rollingStorageStep,
  rollingStorageUnitPrice,
  rollingStorageNums,
  memberNums,
  memberUnitPrice,
  accNums,
  accUnitPrice,
  vipBuyType,
  rollingStorageIsSelected,
  expiry,
  serverTime,
  superviseType,
  addSeatMultiple
}: ComputeTotalPriceType) => {
  if (!rollingStorageIsSelected) rollingStorageNums = 0;
  if (superviseType !== SuperviseType.acc) accNums = 0;
  if (superviseType !== SuperviseType.member) memberNums = 0;

  // 扩容价格
  if (vipBuyType === VIPBuyType.Scaling) {
    const scalingRstPrice = computeScalingTotalPrice({
      unitPrice: multiply(rollingStorageUnitPrice, rollingStorageStep),
      nums: rollingStorageNums,
      expiry
    });
    const scalingPlatAccPrice = computeScalingTotalPrice({
      unitPrice: accUnitPrice,
      nums: accNums,
      expiry,
    });
    const scalingMemberPrice = computeScalingTotalPrice({
      unitPrice: memberUnitPrice,
      nums: memberNums,
      expiry,
    });

    const scalingPrice = add(scalingRstPrice, scalingPlatAccPrice, scalingMemberPrice);
    return toFixed(scalingPrice);
  }

  // 购买和续费价格
  const memberPrice = computeSinglePrice({
    unitPrice: memberUnitPrice,
    nums: memberNums,
    expiry,
    serverTime,
    addSeatMultiple
  });
  const platAccPrice = computeSinglePrice({
    unitPrice: accUnitPrice,
    nums: accNums,
    expiry,
    serverTime,
    addSeatMultiple
  });
  const rollingStoragePrice = computeSinglePrice({
    unitPrice: multiply(rollingStorageUnitPrice, rollingStorageStep),
    nums: rollingStorageNums,
    expiry,
    serverTime,
    addSeatMultiple
  });
  const price = add(rollingStoragePrice, platAccPrice, memberPrice);
  const total = add(price, basePrice);

  return toFixed(total);
};
