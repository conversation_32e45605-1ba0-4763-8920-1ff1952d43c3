import React from 'react';
import { observer } from 'mobx-react';
import { Button, Popup, Space } from 'antd-mobile';
import styles from './style.module.scss';
import RootStore from '@/stores';
import activeBenefits from '../../images/active-benefits.png';
import benefits from '../../images/benefits.png';
import linePng from '../../images/line.png';
import VipPng from '../../images/vip.png';
import ClientRouter from '@/base/client/client-router';
import { APP_ROUTER } from '@/constants';
import { VIPBuyType } from '../../const';

interface IProps {
  visible: boolean;
  onClose: (val: boolean) => void;
  showBuy?: boolean;
  onClickFeeTrial?: () => void;
}

const VipBenefits: React.FC<IProps> = (props) => {
  const { visible, onClose, showBuy = false, onClickFeeTrial } = props;
  const userStore = RootStore.instance.userStore;
  const clientRouter = ClientRouter.getRouter();

  const handleFeeTrial = () => {
    if (onClickFeeTrial) onClickFeeTrial();
  }

  const hanldeUpgrade = () => {
    clientRouter.push(`${APP_ROUTER.VIP_BUY}?type=${VIPBuyType.Buy}`);
  }

  return (
    <div>
      <Popup
        visible={visible}
        onClose={() => {
          onClose(false);
        }}
        onMaskClick={() => {
          onClose(false);
        }}
        bodyStyle={{
          borderRadius: '8px 8px 0 0',
        }}
      >
        <div className={styles.body}>
          <div className={styles.renewBox}>
            <div className={styles.container}>
              <div className={styles.titleWrp}>
                <div className={styles.title}>
                  <span className={styles.text}>安全管家特权</span>
                  <img src={linePng} className={styles.lineIcon} />
                </div>
                <img src={VipPng} className={styles.vipIcon} />
              </div>
            </div>
          </div>

          <div className={styles.imgWrp}>
            <div className={styles.benefitsImgWrp}>
              <img className={styles.benefitsImg} src={userStore.vipAdInfo?.is_in_event ? activeBenefits : benefits} />
            </div>
          </div>

          {showBuy && (<div className={styles['sure']}>
            <div className={styles['sure-content']}>
              <div className={styles.textWrp}>
                {userStore.vipAdInfo?.is_in_event && (<span className={styles.tag}>限时享五天记录</span>)}
                <div className={styles.text}>
                  免费体验：1个平台账号24小时监管
                </div>
              </div>
              <Space className={styles.btnWrp} style={{ '--gap': '8px' }}>
                <Button style={{ color: '#D09E4D' }} onClick={handleFeeTrial}>免费体验</Button>
                <Button color='warning' onClick={hanldeUpgrade} style={{ background: '#D09E4D' }}>立即升级</Button>
              </Space>
            </div>
          </div>)}
        </div>
      </Popup>
    </div>
  );
};

export default observer(VipBenefits);
