import React, { <PERSON> } from 'react';
import { observer } from 'mobx-react';
import style from './style.scss';
import { Space } from 'antd';
import { PayStep, VIPBuyType } from '../../const';
import Header from '../header';
import { HELP_DOCU_LINKS } from '@/constants';
import { adapterHelper } from '@/apis/adapter';
import { SuperEmpty } from '@/components';
import intl from '@/i18n';

const NoVIPAuth: FC = () => {
  const onOpen = () => {
    const url = HELP_DOCU_LINKS.get('vipDoc');
    adapterHelper.openWindow(url, true);
  };
  return (
    <div className={style.box}>
      <div style={{ position: 'relative', left: 18, top: 22 }}>
        <Header vipBuyType={VIPBuyType.Buy} step={PayStep.Success} />
      </div>
      <SuperEmpty
        emptyText={
          <>
            <div className={style.text}>
              <Space size={0} style={{ margin: '4px 0 36px 0' }}>
                <div className={style.tips}>
                  {intl.t('暂无权限操作此功能，请联系上级操作或授权')}
                </div>
              </Space>
              <div className={style.btns}>
                <Space size={8}>
                  <div onClick={onOpen} className={style.check}>
                    {intl.t('查看紫鸟安全管家-专业版权益>')}
                  </div>
                </Space>
              </div>
            </div>
          </>
        }
      />
    </div>
  );
};

export default observer(NoVIPAuth);
