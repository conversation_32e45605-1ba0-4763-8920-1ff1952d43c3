import { makeObservable, computed, observable, runInAction, reaction } from 'mobx';
import {
  ClientBridge,
  isNil,
  type LoginService,
  LoginServicePayload,
  to,
  clientConfig,
} from '@ziniao-fe/core';
import { Logs } from '@/utils';
import configs, { iosEnv } from '@/configs';
import { loginDB } from '@/db';
import localConfig, { iOSLocalMachineString } from '@/configs/ios.local';
import ClientRouter from '@/base/client/client-router';
import type IosRouter from '@/base/client/ios/ios-router';
import { tools } from '@/utils/tools';
import { IOS_LOGIN_CHANGE } from '@/constants';
import { vConsole } from '@/utils/logs';
import { CACHED_LOGIN_INFO_KEY } from '@/constants';

interface IBroadcastResult<T = any> {
  name?: string;
  value: T;
  uuid: string;
}
export type IMessageListener = <T = any>(data: T) => any;

class iOSSdk {
  static CLIENT_NAME = 'SBiOS';
  private static pan_gu_epoch = 'g0MW7KcyAX6DaEBT5fsP';
  /** 客户端缓存可配置环境的key固定一个 */
  private static CACHED_CONFIG_KEY = 'GetApiPreUrlInfo';
  /** 客户端缓存用户信息字段 */
  private static CACHED_LOGIN_INFO_KEY = CACHED_LOGIN_INFO_KEY;
  /**
   * iOS bridge对象
   */
  private bridge = new ClientBridge(/* iOSSdk.CLIENT_NAME */);
  private _bridgeReady: boolean = false;
  /** 登录信息 */
  private loginInfo: LoginService.LoginUserInfo | null = null;

  private onLoginInfoChange?: (loginInfo: LoginService.LoginUserInfo | null) => void;
  public clientRouter: IosRouter;

  constructor() {
    makeObservable(this, {
      connected: computed,
      // @ts-ignore
      loginInfo: observable,
      bridge: observable,
      _bridgeReady: observable,
    });
    // 代理 this.bridge._ready
    Object.defineProperty(this.bridge, '_ready', {
      get: () => this._bridgeReady,
      set: (value) => {
        runInAction(() => {
          this._bridgeReady = value;
        });
      },
    });

    // iOS前端模拟客户端发出登录信息变更广播
    reaction(
      () => this.loginInfo,
      (info: LoginService.LoginUserInfo | null) => {
        this.onLoginInfoChange?.(info);
      }
    );
    this.clientRouter = ClientRouter.getRouter(this.bridge) as IosRouter;
  }

  /**
   * 已经成功连接
   * @computed
   */
  get connected() {
    console.log('[ios] bridge ready', this._bridgeReady);
    return !!this._bridgeReady;
    // return true;
  }

  /** 注册广播回调 */
  registerBroadcast = <T = any>(api: string, listener: IMessageListener) => {
    return this.bridge?.addListener(api, (result: IBroadcastResult) => {
      console.warn(`[iOS] Receive client broadcast: ${api}`);
      Logs.log(result);

      listener(result.value as T);
    });
  };

  /** 移除广播回调 */
  removeBroadcast = (api: string, listener: IMessageListener) => {
    Logs.log('[iOS] Remove client broadcast', `${api}`);

    return this.bridge?.removeListener(api, listener);
  };

  /**
   * 写客户端
   * @description iOS很多数据都是通过这个方法获取到数据，比如机器码那些
   */
  getStorage = async <T = any>(key: string, session = false): Promise<null | T> => {
    const [err, response] = await to(
      this.bridge.call<string>(session ? 'getInstanceValue' : 'readData', { key })
    );
    Logs.warn('[iOS] Get storage', key, err, response);
    if (err) return null;
    try {
      const result = tools.safeParseJSON(response || 'null');
      const expireTime = result?.expireTime;
      const data = result?.data || result;
      const now = new Date().getTime();
      // 过期自动处理
      if (expireTime && now > expireTime) {
        Logs.warn('[iOS] Storage expired, key: ${key}');
        this.setStorage(key, null); // 过期置空

        return null;
      }
      Logs.warn('[iOS] Get storage', key, data, response);
      return data;
    } catch (error) {
      return null;
    }
  };

  setStorage = async (
    key: string,
    value: any,
    options: {
      session?: boolean;
      expireTime?: null | number;
    } = {
      session: false,
      expireTime: null,
    }
  ) => {
    // 可序列化的值
    const stringify = value ?? null;

    try {
      const message = stringify
        ? JSON.stringify({
            data: stringify,
            expireTime: options.expireTime,
          })
        : '';
      const [err, response] = await to(
        this.bridge.call(!!options?.session ? 'setInstanceValue' : 'writeData', {
          key,
          value: message,
        })
      );

      if (err) throw err;

      return;
    } catch (error) {
      console.error(`[iOS] Write client storage failed, key: ${key}, value: ${stringify}`);
      throw error;
    }
  };

  /**
   * 获取机器码信息
   */
  getMachineInfo = async () => {
    // if (__DEV__) {
    //   return {
    //     clientEncodeCode: iOSSdk.pan_gu_epoch,
    //     macAddresses: [],
    //     machineName: 'IOS',
    //     machineCode: iOSLocalMachineString,
    //     machineCodeNew: iOSLocalMachineString,
    //   };
    // }
    const [err, machineString] = await to(
      this.bridge.call('getInstanceValue', {
        key: 'deviceID',
      })
    );
    // const macAddresses = await this.getMacString();
    Logs.log('[iOS] Get machine info', machineString, err);
    const info: SuperClient.MachineInfoData = {
      clientEncodeCode: iOSSdk.pan_gu_epoch,
      macAddresses: [],
      machineName: 'IOS',
      machineCode: machineString!,
      machineCodeNew: machineString!,
    };

    return info;
  };

  /**
   * 获取当前版本号
   */
  getVersion = async () => {
    // const [err, version] = await to(this.getStorage<string>(
    //   'appVersion',
    //   true
    // ));
    const [err, version] = await to<string>(this.bridge.call('getVersionString'));
    if (err) return;

    return version;
  };

  /**
   * 设置客户端配置URL
   * @param config 序列化过后的字符串
   */
  setClientConfig = async (config: string) => {
    const [err, response] = await to(this.bridge.call(iOSSdk.CACHED_CONFIG_KEY, config));
    if (err) return;

    return response;
  };
  getIosEnv = async () => {
    const [err, response] = await to(this.bridge.call<iOSClient.Env>('getEnv'));
    if (err) return;

    return response;
  };

  /** 获取当前客户端配置 */
  getClientConfig = async (): Promise<SuperClient.Config> => {
    const clientEnv = await this.getIosEnv();
    /**取不到值，走默认的production*/
    let env = iosEnv.Release;
    if (clientEnv && !!iosEnv?.[clientEnv]) {
      env = iosEnv?.[clientEnv!];
    }
    let envid = (import.meta.env.DEV ? 'dev' : env) as IRuntime.Env;
    // 运行时前端默认配置
    const runtimeConf: IRuntime.EnvConfig = configs[envid];
    console.log(`[客户端环境]:${envid}获取到的配置信息：`, runtimeConf);
    const commonConf = {
      env: envid,
      officialWebsite: runtimeConf.official,
      fadadaUrl: runtimeConf.fadadaUrl,
      webRemoteUrl: runtimeConf.webRemoteUrl,
      sems: runtimeConf.host,
      ssos: runtimeConf.SSOS,
      adminPage: '',
    };

    if (env === iosEnv.Release) {
      const vconsole = await this.getStorage('vconsole');
      vconsole ? vConsole.showSwitch() : vConsole.hideSwitch();
    } else {
      vConsole.showSwitch();
    }
    // 生产环境的包永远用默认的配置不变动
    if (env === 'production') {
      return commonConf;
    }
    const [err, res] = await to(this.getCachedConfigByKey('GetApiPreUrlInfo'));
    if (err) {
      return commonConf;
    }
    /**！！注意这里，data返回的是缓存的值*/
    return {
      ...commonConf,
      sems: res.SEMS,
      ssos: res.SSOS,
    };
  };

  /**
   * 设置登录的信息
   * @description iOS端登录是由前端直接发起接口请求成功后，主动将信息存在客户端持久化数据中
   */
  setLoginInfo = async (loginInfo: LoginService.LoginUserInfo | null) => {
    // 可以先提前
    runInAction(() => {
      this.loginInfo = loginInfo;
    });

    // if (__DEV__) {
    //   if (isNil(loginInfo)) {
    //     loginDB.remove(iOSSdk.CACHED_LOGIN_INFO_KEY);
    //   } else {
    //     loginDB.set(iOSSdk.CACHED_LOGIN_INFO_KEY, loginInfo);
    //   }

    //   return loginInfo;
    // }

    try {
      await this.setStorage(iOSSdk.CACHED_LOGIN_INFO_KEY, loginInfo);
    } catch (error) {
      Logs.error('[logininfo] Set loginInfo', error);
    }
    const [err, result] = await to(
      this.bridge.call<LoginService.LoginUserInfo>(
        'setLoginInfo',
        tools.safeStringifyJSON(loginInfo || {})
      )
    );
    if (err) return;
    Logs.log('[logininfo] setLoginInfo->success', result, err);
    return result;
  };

  /** 获取记录在客户端缓存中的登录的用户信息 */
  getLoginInfo = async () => {
    // if (__DEV__) {
    //   const localLoginInfo = loginDB.get(iOSSdk.CACHED_LOGIN_INFO_KEY) ?? null;
    //   runInAction(() => {
    //     this.loginInfo = localLoginInfo;
    //   });

    //   return localLoginInfo;
    // }
    Logs.log('[logininfo]  getLoginInfo');
    const [err, result] = await to(
      this.getStorage<LoginService.LoginUserInfo>(iOSSdk.CACHED_LOGIN_INFO_KEY)
    );
    if (err) return;
    Logs.log('[logininfo] getLoginInfo', result);
    runInAction(() => {
      this.loginInfo = result;
    });

    return result;
  };

  logout = async () => {
    // if (__DEV__) {
    //   this.setLoginInfo(null);

    //   return true;
    // }
    this.setLoginInfo(null);
    await to(this.bridge.call('logout'));
    // 单方面退出即可，不用等状态
    return true;
  };

  onUserChange = (callback: (loginInfo: LoginService.LoginUserInfo | null) => void) => {
    // this.registerBroadcast(IOS_LOGIN_CHANGE, () => {
    //   console.log('[ios]改变用户重新加载');
    //   location.reload();
    // });
    this.onLoginInfoChange = callback;
  };

  getPublicProperties = async () => {
    const [err, response] = await to(this.bridge.call('getSensorSuperProperties'));
    if (err) return;

    return response;
  };
  buriedPoint = async (eventName: string, params: Record<string, any>) => {
    const [err, response] = await to(this.bridge.call('recordEvent', { eventName, params }));
    if (err) return;
    return response;
  };

  /** 发起 apple pay 内购流程 */
  startIpaPurchase = async (params: { productID: string; orderID: string }) => {
    const [err, response] = await to(this.bridge.call('startIpaPurchase', params));
    if (err) return;

    return response;
  };

  /** 检查更新 */
  checkUpdate = async () => {
    const [err, response] = await to(this.bridge.call('checkUpdate'));
    if (err) return;

    return response;
  };
  setTabIndicatorNumber = async (params: { index: number; number: number }) => {
    const [err, response] = await to(this.bridge.call('setTabIndicatorNumber', params));
    if (err) return;

    return response;
  };
  /** 读取配置URL */
  getCachedConfigByKey = async (key: string) => {
    const res = await this.bridge.call('getCachedConfigByKey', key);
    return res;
  };
  /** 设置配置URL */
  setCachedConfigByKey = async (key: string, value: any) => {
    const res = await this.bridge.call('setCachedConfigByKey', {
      key,
      value,
    });
    return res;
  };
  commonBroadCast = async (params: { name: string; value: { msg: string } }) => {
    const [err, response] = await to(this.bridge.call('broadCast', params));
    if (err) return;

    return response;
  };
}

export default iOSSdk;
