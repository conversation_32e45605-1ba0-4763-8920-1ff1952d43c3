import React, { useEffect, useMemo, useState } from 'react';
import { AiOutlineHome, AiOutlineRight } from 'react-icons/ai';
import { observer, useLocalObservable } from 'mobx-react';

import HeaderNavbar from '@/components/header-navbar';
import Store from './store';
import { Button, DotLoading, Radio, Toast } from 'antd-mobile';
import SubscriptionOptions from '@/components/swiper-option';

import styles from './styles.module.scss';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { APP_ROUTER, netWorkTypesMap, WORKBENCH_URL } from '@/constants';
import { isNaN, set } from 'lodash';
import PayMethods from '@/components/pay-methods';
import { PAY_METHODS } from '@/components/pay-methods/const';
import SuperToast from '@/components/super-toast';
import CouponBox from '@/components/coupon-box';
import DeviceCost from './components/devive-cost';
import RootStore from '@/stores';
import IosPayPopup from '@/components/ios-pay-popup';
import ClientRouter from '@/base/client/client-router';
import { CloseAliPayAndWxPay, isH5Program } from '@/utils/platform';
import { NetWorkTypes } from '@/types/core/Device';
import { useWechatAuth } from '@/components/wechat-auth';
import { detectWechatEnvironment } from '@/utils/wechat-pay-utils';
interface CouponData {
  id: string;
  amount: number;
  minAmount: number;
  startDate: string;
  endDate: string;
  isDisabled: boolean;
}

const DeviceRenewal: React.FC = () => {
  const userStore = RootStore.instance.userStore;
  const [searchParams] = useSearchParams();
  let query = searchParams.get('ids');
  const clientRouter = ClientRouter.getRouter();
  let ids = [];
  if (query) {
    ids = JSON.parse(window.atob(query || '') || '[]');
  }
  const store = useLocalObservable<Store>(() => new Store(ids));
  const [IosPayVisible, setIosPayVisible] = useState(false);

  // 微信授权相关
  const wechatEnv = detectWechatEnvironment();
  const { openid, isAuthorizing, startAuth, isAuthorized } = useWechatAuth();

  // useEffect(() => {
  //   let query = searchParams.get('ids');
  //   if (query) {
  //     let ids = JSON.parse(window.atob(query || '') || '[]');
  //     if (ids?.length > 0) {
  //       store.getDetail(ids);
  //     }
  //   }
  // }, [searchParams.get('ids')]);

  useEffect(() => {
    if (store.balanceManager?.data) {
      store.autoSetPaymentMethod();
    }
  }, [store.balanceManager.data?.balance, store.paymentAmount]);

  const durationsOptions = useMemo(() => {
    const options = store.durations?.filter((item) => {
      if (store.isRenewLocal) {
        return true;
      } else {
        const showExtendDay = !!item.extend && !store.isRenewLocal && !store.isRenewSelfIP;
        if (showExtendDay) {
          item.tag = `送${item.extend}天`;
          if (!item.isDay && !item.isHour) {
            item.extra = `省￥${
              (item.extend / 30) *
              store.renewManager?.originData?.reduce((pre, cur) => pre + cur?.cost, 0)
            }`;
          }
        }
        return !item.isDay;
      }
    });
    return options;
  }, [store.durations, store.isRenewLocal, store.isRenewSelfIP, store.renewManager?.originData]);

  const handleSelect = (opt) => {
    store.setCurrentTicket(null);
    store.onChangePayChoose({ duration: opt });
  };

  const paySuccessCallback = async () => {
    // navigate(APP_ROUTER.EXPIRING_DEVICES_LIST);
    Toast.clear();
    Toast.show({
      icon: 'success',
      content: '支付成功',
      duration: 3000,
      maskClickable: false,
      afterClose: () => {
        // clientRouter.push(APP_ROUTER.EXPIRING_DEVICES_LIST);
        clientRouter.goBack();
      },
    });
  };

  const payFailedCallback = async (data) => {
    Toast.clear();
    store.setCurrentTicket(null);
    SuperToast.error(data?.msg, 2);
    store.setOrderId();
    await store.getCoupons();
  };

  const handlePay = async () => {
    const balance = store?.balanceManager?.data?.balance;

    // 微信公众号支付授权检查
    if (store.payMethod === PAY_METHODS.WECHAT_JSAPI) {
      const wechatEnv = detectWechatEnvironment();
      if (!wechatEnv.isWechatBrowser) {
        Toast.show({
          icon: 'fail',
          content: '请在微信中打开',
          duration: 3000,
        });
        return;
      }

    }

    /**检测余额是否充足 */
    if (isH5Program()) {
      if (Number(balance || '0') < store.paymentAmount) {
        Toast.show({
          icon: 'fail',
          content: '余额不足',
          duration: 3000,
        });
        return;
      }
    }

    Toast.show({
      icon: 'loading',
      content: '支付中...',
      duration: 0,
      maskClickable: false,
    });

    const errData = await store.onGenerateOrder();
    if (__IOS_CLIENT__) {
      setIosPayVisible(true);
      return;
    }

    if (
      store.payMethod != PAY_METHODS.BALANCE &&
      store.payMethod != PAY_METHODS.CREDIT &&
      !errData
    ) {
      const result = await store.onPay();
      if (result?.isSuccess) {
        paySuccessCallback();
      } else {
        payFailedCallback(result);
      }
    } else {
      if (!errData) {
        paySuccessCallback();
      } else {
        payFailedCallback({ msg: errData?.message || '支付失败' });
      }
    }
  };

  const goBack = () => {
    clientRouter.goBack();
  };

  useEffect(() => {
    if (store.pageLoading) {
      Toast.show({
        icon: 'loading',
        duration: 0,
        maskClickable: false,
      });
      setTimeout(() => {
        Toast.clear();
      }, 10000);
    } else {
      Toast.clear();
    }
  }, [store.pageLoading]);

  return (
    <div className={styles.body}>
      <div className={styles.top}>
        <HeaderNavbar title="设备续费" onBack={goBack}></HeaderNavbar>
      </div>
      <div className={styles.renewBox}>
        <div className={styles.container}>
          <div className={styles['container-title']}>设备信息</div>
          <div className={` ${styles['device-card']}`}>
            {store.renewManager.originData.map((item) => (
              <div className={styles['info-box']} key={item.id}>
                <div className={styles['item-title']}>
                  <div className={styles.proxyName}>{item.proxy_name}</div>
                  {/* <div className='color-danger'>¥{item.current_cost}/月</div> */}
                  <DeviceCost store={store} row={item} />
                </div>
                <div className={styles['text-gray']}>设备信息：{item.ip}</div>
                <div className={styles['text-gray']}>
                  类型/套餐：
                  {netWorkTypesMap.get(Number(item.network_type))}
                  {netWorkTypesMap.get(Number(item.network_type)) && item?.cloud_name && '，'}
                  {item?.cloud_name}
                </div>
                <div className={styles['text-gray']}>
                  归属地区：
                  {item?.area_name}
                  {item?.area_name && item?.city_name && item?.type != 1 && '，'}
                  {item?.type != 1 && item?.city_name}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className={styles.container}>
          <div className={styles['container-title']}>订单价格</div>
          <div className={` ${styles['device-card']}`}>
            <div className={styles['info-box']}>
              <div className={`${styles['item-title']} ${styles['price-font']}`}>
                <div>订单总价</div>
                {!isNaN(store.originalTotal) ? (
                  <div className="black">¥{store.originalTotal?.toFixed(2)}</div>
                ) : (
                  <div className="black">-</div>
                )}
              </div>
              <div className={`${styles['item-title']} ${styles['price-font']}`}>
                <div>优惠金额</div>
                {!isNaN(store.discountAmount) ? (
                  <div className="color-danger">-¥{store.discountAmount?.toFixed(2)}</div>
                ) : (
                  <div className="color-danger">-</div>
                )}
              </div>
              <div
                className={`${styles['item-title']} ${styles['price-font']}`}
                onClick={() => store.setCouponsVisible(true)}
              >
                <div>优惠券</div>
                <div className={styles.couponItem}>
                  {store.tickets.canuseData.length > 0 ? (
                    <span className={styles.hasCoupon}>
                      <span>{store.tickets.canuseData.length}张可用</span>
                      <AiOutlineRight />
                    </span>
                  ) : (
                    <span className={styles.noCoupon}>
                      <span>暂无可用</span>
                      <AiOutlineRight />
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className={`${styles.amount} ${styles['price-font']}`}>
              <div>应付金额</div>
              {!isNaN(store.paymentAmount) ? (
                <div>
                  {!isNaN(store.discountAmount) && (
                    <span className={styles['red-font']}>
                      已优惠¥{store.discountAmount?.toFixed(2)}
                    </span>
                  )}
                  <span className={styles['big-font']}>¥{store.paymentAmount?.toFixed(2)}</span>
                </div>
              ) : (
                <div>-</div>
              )}
            </div>
          </div>
        </div>
        <div className={styles.container}>
          <div className={styles['container-title']}>续费时长</div>
          <SubscriptionOptions
            selectedValue={store.payChooseDetail?.duration?.value}
            onSelect={handleSelect}
            options={durationsOptions || []}
          />
          {store.isRenewPlatform && (
            <div className={styles.tips}>
              <div>
                <span style={{ color: 'red' }}>注：</span>购买时长6个月，赠送免费替换设备1次；
              </div>
              <div>购买时长12个月，赠送免费替换设备3次。</div>
            </div>
          )}
        </div>
        {!__IOS_CLIENT__ && (
          <div className={styles.container}>
            <div className={styles['container-title']}>支付方式</div>
            <div className={styles.payBox}>
              <PayMethods
                getBalance={store.getBalance}
                balance={store?.balanceManager?.data?.balance}
                currentPayMethod={store.payMethod}
                onChangePayMethods={(val) => {
                  store.setPayMethod(val);
                }}
                payMoney={store.paymentAmount}
              />
            </div>
          </div>
        )}
        <CouponBox
          ticketsData={store.tickets.data}
          currentTickets={store.currentTickets}
          forbidChooseSpecialTicket={store.forbidChooseSpecialTicket}
          couponsVisible={store.couponsVisible}
          availableParams={{
            chooseDayOrHour: store.chooseDayOrHour,
            payChooseDetail: store.payChooseDetail,
            isRenewPage: store.isRenewPage,
            isRenewLocal: store.isRenewLocal,
            isRenewSelfIP: store.isRenewSelfIP,
            currentTickets: [],
            amountOriginalTotal: store.originalTotal,
            amountTotal: store.orderTotal,
            amountPromotions: store.promotionDiscountAmount,
            entDiscount: store.corporateDiscountAmount,
            vipDiscount: store.vipDiscountAmount,
          }}
          payPreferntial={store.payPreferntial}
          setCouponsVisible={store.setCouponsVisible}
          setCurrentTicket={store.setCurrentTicket}
          setCanuseTickets={store.setCanuseTickets}
          isRenewPage={store.isRenewPage}
          isRenewLocal={store.isRenewLocal}
          isRenewSelfIP={store.isRenewSelfIP}
          payChooseDetail={store.payChooseDetail}
          renewManager={store.renewManager}
          localPackages={store.localPackages}
        />
      </div>

      <div className={styles['sure']}>
        <div>
          <div>
            应付金额：
            {!isNaN(store.paymentAmount) ? (
              <span className="color-danger" style={{ fontSize: '4.8vw' }}>
                ¥{store.paymentAmount?.toFixed(2)}
              </span>
            ) : (
              <span className="color-danger" style={{ fontSize: '4.8vw' }}>
                -
              </span>
            )}
          </div>
          {!isNaN(store.discountAmount) ? (
            <div className={styles['red-font']}>已优惠¥{store.discountAmount?.toFixed(2)}</div>
          ) : (
            <div className={styles['red-font']}>已优惠-</div>
          )}
        </div>
        <Button block color="primary" loading={store.generateLoading} onClick={handlePay}>
          立即支付
        </Button>
      </div>
      {__IOS_CLIENT__ && (
        <IosPayPopup
          createOrder={store.iOSonPay}
          visiblePopup={IosPayVisible}
          loadingPay={store.loadingPay}
          onClose={() => {
            setIosPayVisible(false);
          }}
        />
      )}
    </div>
  );
};
export default observer(DeviceRenewal);
