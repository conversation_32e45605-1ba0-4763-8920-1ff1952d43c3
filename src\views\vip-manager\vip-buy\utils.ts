
import intl from '@/i18n';
import RootStore from '@/stores';
import dayjs from 'dayjs';
import { PaymentTypes, VIPBuyType } from './const';
import { BuyVipSourceSeat } from './type';

export enum SuperviseType {
  SuperviseAccount = 0,
  SuperviseMember = 1,
}

export interface BuyVipUrlParams {
  /** VIP 购买类型 */
  type: VIPBuyType.Buy;
  /** 默认加购账号数量 */
  acc?: string;
  /** 默认加购成员数量 */
  member?: string;
  /** 回退地址 例：/home 跳转首页*/
  backUrl?: string;
  /** 埋点 */
  sourceSeat?: string;
}

/** 获取会员状态 */
export const getVipStatus = (data: VipInfo) => {
  const { extraInfo } = RootStore.instance?.userStore;
  const isVipMember = data?.is_vip_member;
  const isAutoRenew = !!data?.is_auto_renew && isVipMember;
  let isExpireding = false;
  let isExpired = false;
  let isAccSupervise = false;
  let isMemberSupervise = false;

  const isVipPurchased = data?.is_vip_purchased;
  let isVipTryout = false;
  let isVipTryoutExpired = false;
  let isSuperviseEnabled = extraInfo?.is_supervise_been_enabled;
  if (!isAutoRenew) {
    const diffInDays = dayjs(data?.expiry * 1000).diff(dayjs(RootStore.instance?.systemStore?.serverTime), 'day', true);
    if (diffInDays < 7 && isVipMember) {
      isExpireding = true;
    }
    if (diffInDays <= 0 && !isVipMember && data?.expiry) {
      isExpired = true;
    }
    if (!isVipPurchased && !isVipMember) {
      isVipTryout = data?.is_vip_tryout;
      isVipTryoutExpired = !data?.is_vip_tryout;
    }
  }

  if (data?.supervise?.supervise_type === SuperviseType.SuperviseAccount) {
    isAccSupervise = true;
  } else if (data?.supervise?.supervise_type === SuperviseType.SuperviseMember) {
    isMemberSupervise = true;
  }

  return {
    /** 是否会员  */
    isVipMember,
    /** 是否即将过期 */
    isExpireding,
    /** 是否过期 */
    isExpired,
    /** 自动续费 */
    isAutoRenew,
    /** 监管类型 */
    superviseType: data?.supervise?.supervise_type,
    /** 按账号监管 */
    isAccSupervise,
    /** 按成员监管 */
    isMemberSupervise,
    /** 是否体验会员 */
    isVipTryout,
    /** 体验会员是否过期 */
    isVipTryoutExpired,
    /** 是否有开启过事中监管 */
    isSuperviseEnabled,
    /** 是否购买过会员 */
    isVipPurchased,
  };
};

/** 是否需要展示vip介绍弹窗 */
export const isNeedShowVipVipIntroduce = (data: VipInfo) => {
  const { isVipMember, isSuperviseEnabled } = getVipStatus(data);
  if (!isSuperviseEnabled && !isVipMember) {
    return true;
  } else {
    return false;
  }
};

/** 加购是否即将到期 */
export const getAddPackageIsExpireding = (time: number) => {
  const serverTime = RootStore.instance.systemStore.serverTime;
  let isExpireding = false;
  const diffInDays = dayjs(time * 1000).diff(serverTime, 'day');
  if (diffInDays <= 7) {
    isExpireding = true;
  }
  return isExpireding;
};

export const vipBuySensorHelper = {
  // getBuyType: (type: VIPBuyType) => {
  //   switch (type) {
  //     case VIPBuyType.Buy:
  //       return BuriedPoint.Config?.VIP?.PAGE_VIEWS?.PAGE_NAMES?.BUY;
  //     case VIPBuyType.Renew:
  //       return BuriedPoint.Config?.VIP?.PAGE_VIEWS?.PAGE_NAMES?.RENEW;
  //     case VIPBuyType.Scaling:
  //       return BuriedPoint.Config?.VIP?.PAGE_VIEWS?.PAGE_NAMES?.PURCHASE_SEPARATELY;
  //     default:
  //       return '';
  //   }
  // },
  // getPayMethod: (type: PaymentTypes) => {
  //   switch (type) {
  //     case PaymentTypes.Ali:
  //       return BuriedPoint.Config?.VIP?.PAGE_VIEWS?.PAY_METHOD?.ALI;
  //     case PaymentTypes.Wechat:
  //       return BuriedPoint.Config?.VIP?.PAGE_VIEWS?.PAY_METHOD?.WE_CHAT;
  //     case PaymentTypes.Balance:
  //       return BuriedPoint.Config?.VIP?.PAGE_VIEWS?.PAY_METHOD?.BALANCE;
  //     case PaymentTypes.Aggregate:
  //       return BuriedPoint.Config?.VIP?.PAGE_VIEWS?.PAY_METHOD?.AGGREGATE;
  //     default:
  //       return '';
  //   }
  // },
  // viewVipPage: (type: VIPBuyType, sourceSeat: string | number, sourceGroup: any, pageWhere: any) => {
  //   const buyType = vipBuySensorHelper.getBuyType(type);
  //   superSensorsTool.track(BuriedPoint.Config.VIP.PAGE_VIEWS.VIP_PAGE, {
  //     buy_type: buyType,
  //     source_seat:
  //       BuriedPoint.Config.VIP.PAGE_VIEWS.BUY_SOURCE_SEAT[
  //       sourceSeat as keyof typeof BuriedPoint.Config.VIP.PAGE_VIEWS.BUY_SOURCE_SEAT
  //       ] || sourceSeat,
  //     source_group: sourceGroup,
  //     page_where: pageWhere,
  //   });
  // },
  // viewCenterVipPage: () => {
  //   superSensorsTool.track(BuriedPoint.Config.VIP.PAGE_VIEWS.VIP_CENTER);
  // },
  // /** 打开会员介绍弹窗 */
  // viewMemberModal: (key: keyof typeof BuriedPoint.Config.VIP.PAGE_VIEWS.SOURCE_SEAT) => {
  //   superSensorsTool.track(BuriedPoint.Config.VIP.PAGE_VIEWS.SHIZHONGBUZU_PAGE, {
  //     sourceSeat: BuriedPoint.Config.VIP.PAGE_VIEWS.SOURCE_SEAT[key],
  //   });
  // },
  // buyVip: (data: {
  //   pay_method: PaymentTypes;
  //   type: VIPBuyType;
  //   buy_period: number;
  //   order_id: string;
  //   sourceSeat: BuyVipSourceSeat;
  //   sourceGroup: string;
  //   pageWhere: string;
  // }) => {
  //   const buyType = vipBuySensorHelper.getBuyType(data?.type);
  //   const payMethod = vipBuySensorHelper.getPayMethod(data?.pay_method);
  //   superSensorsTool.track(BuriedPoint.Config.VIP.PAGE_VIEWS.SUPERBROWSER_BUY_VIP_ONLINE, {
  //     buy_type: buyType,
  //     order_id: data?.order_id,
  //     buy_period: intl.t('{slot0}天', { slot0: data?.buy_period }),
  //     pay_method: payMethod,
  //     sourceSeat: BuriedPoint.Config.VIP.PAGE_VIEWS.BUY_SOURCE_SEAT[data?.sourceSeat] || data?.sourceSeat,
  //     source_group: data.sourceGroup,
  //     page_where: data?.pageWhere,
  //   });
  // },

  // viewSuperViseModal: (key: keyof typeof BuriedPoint.Config.VIP.PAGE_VIEWS.SUPERVISE_MODAL_SOURCE) => {
  //   const data = BuriedPoint.Config.VIP.PAGE_VIEWS.SUPERVISE_MODAL_SOURCE[key];
  //   superSensorsTool.track(data?.EVENT, {
  //     page_name: data?.PAGE_NAME,
  //   });
  // },
};
