import { observer, useLocalStore } from 'mobx-react';
import { FC, useEffect, useState } from 'react';
import Store from './store';
import SelectPackage from './components/select-package';
import styles from './style.module.scss';
import { Spin } from 'antd';
import { useCreation, useInterval } from 'ahooks';
import { useSearchParams } from 'react-router-dom';
import UserStore from '@/stores/user';
import RootStore from '@/stores';
import AuthStore from '@/stores/auth';
import { useInjectedStore } from '@/hooks/useStores';
import VipBenefits from './components/vip-benefits';
import ClientRouter from '@/base/client/client-router';
import HeaderNavbar from '@/components/header-navbar';
import { VIPBuyType } from './const';

const VipBuy: FC = () => {
  const store = useLocalStore(() => new Store());
  const [search] = useSearchParams();
  const userStore: UserStore = useInjectedStore('userStore');
  const authStore: AuthStore = useInjectedStore('authStore');
  const { hasVIPBuyAuth, permissionCodes, hasPurchaseDeviceAuth, } = authStore;
  const [visibleBenefits, setVisibleBenefits] = useState(false);
  const clientRouter = ClientRouter.getRouter();

  const clear = useInterval(
    () => {
      RootStore.instance.systemStore.getServerTime();
    },
    60000,
    { immediate: true }
  );

  useEffect(() => {
    store.init();

    return () => {
      clear();
    }
  }, []);

  const navTitle = useCreation(() => {
    if (store.VIPBuyType === VIPBuyType.Buy) {
      return '购买增值服务'
    }
    if (store.VIPBuyType === VIPBuyType.Renew) {
      return '续费增值服务'
    }
    if (store.VIPBuyType === VIPBuyType.Scaling) {
      return '加购增值服务'
    }

    return '安全管家'
  }, [store.VIPBuyType])

  // if (!hasVIPBuyAuth && permissionCodes.length > 0) {
  //   return <NoVIPAuth />;
  // } 
  return (
    <>
      <Spin
        size="small"
        wrapperClassName={styles.vipSpin}
        style={{ height: '100%' }}
        spinning={store.packageLoading || store.expandPackage.loading}
      >
        <div className={styles.vipManagerWrp}>
          <HeaderNavbar
            title={navTitle}
            onBack={() => {
              clientRouter.goBack();
            }}
            rightNode={<span className={styles.viewBenefits} onClick={() => setVisibleBenefits(true)}>查看特权</span>}
          />
          <div className={styles.vipHeader}>
            <div className={styles.header}>
              <span className={styles.title}>安全管家</span>
              <span className={styles.desc}>
                {store.VIPBuyType === VIPBuyType.Buy ?
                  '成员店铺内操作行为监管' :
                  `${userStore.vipInfo?.expiry_str} 到期`
                }
              </span>
            </div>
          </div>
          <SelectPackage store={store} />
        </div>
      </Spin>

      <VipBenefits
        visible={visibleBenefits}
        onClose={() => {
          setVisibleBenefits(false);
        }}
        showBuy={false}
      // showBuy={!userStore.vipInfo?.is_vip_member && hasVIPBuyAuth}
      />
    </>
  );
};

export default observer(VipBuy);
