import React from 'react';
import { observer } from 'mobx-react';
import HeaderNavbar from '@/components/header-navbar';
import styles from './styles.module.scss';
import ClientRouter from '@/base/client/client-router';

const VipBuyServiseProtocol: React.FC = observer(() => {
  const clientRouter = ClientRouter.getRouter();

  return (
    <div className={styles.link}>
      <HeaderNavbar
        onBack={() => clientRouter.goBack()}
        title="紫鸟增值服务协议"
      ></HeaderNavbar>
      <iframe className={styles.iframe} src="https://test-www.ziniao.com/vip-agree.html"></iframe>
    </div>
  );
});

export default VipBuyServiseProtocol;
