import { observer } from 'mobx-react';
import { FC } from 'react';
import { SuperviseType, VIPBuyType } from '../../../const';
import { ComputeTotalPriceType, toFixed } from '../../../price-compute';
import Store from '../../../store';
import styles from './style.module.scss';
import RootStore from '@/stores';
import { multiply } from 'mathjs';
import { Stepper } from 'antd-mobile';

interface IProps {
  store: Store;
}

const findMinNum = (num1: number, num2: number) => {
  if (num1 > num2) {
    return num2;
  }

  return num1;
};
const ExpandPackage: FC<IProps> = ({ store }) => {
  const {
    changePriceSource,
    priceSource,
    expandPackage,
    selectedPackage,
    membersPrice,
    accPrice,
    superviseType,
    predictAddNumData,
  } = store;
  const { systemStore } = RootStore.instance;
  const globalCurrency = systemStore.globalCurrency;

  const handlePriceSourceChange = (key: keyof ComputeTotalPriceType, value: number) => {
    changePriceSource(key, value);
  };

  const dataSource = [
    {
      name: expandPackage.member?.remark,
      base: selectedPackage?.equity?.seat,
      unitPrice: priceSource?.memberUnitPrice,
      totalPrice: membersPrice,
      discountPrice: priceSource.memberUnitPrice,
      price: expandPackage?.member?.price,
      min: expandPackage.member?.option?.min,
      max: expandPackage.member?.option?.max,
      step: expandPackage.member?.step,
      nums: priceSource?.memberNums,
      predictAddNum:
        store.VIPBuyType === VIPBuyType.Buy
          ? findMinNum(predictAddNumData?.user_count, predictAddNumData?.store_count)
          : predictAddNumData?.user_count,
      key: 'memberNums',
      hide: superviseType !== SuperviseType.member,
    },
    {
      name: expandPackage.account?.remark,
      base: selectedPackage?.equity?.seat,
      unitPrice: priceSource?.accUnitPrice,
      discountPrice: priceSource.accUnitPrice,
      totalPrice: accPrice,
      price: expandPackage?.account?.price,
      min: expandPackage.account?.option?.min,
      max: expandPackage.account?.option?.max,
      step: expandPackage.account?.step,
      nums: priceSource?.accNums,
      key: 'accNums',
      predictAddNum:
        store.VIPBuyType === VIPBuyType.Buy
          ? findMinNum(predictAddNumData?.user_count, predictAddNumData?.store_count)
          : predictAddNumData?.store_count,
      hide: superviseType !== SuperviseType.acc,
    },
  ].filter((item) => !item?.hide);

  // const getNameEl = (row: any, type: VIPBuyType) => {
  //   if (type === VIPBuyType.Scaling) {
  //     return row.name;
  //   }

  //   if (row.key === 'memberNums') {
  //     return (
  //       <Space direction="vertical" size={4}>
  //         <span className={styles.typeName}>{intl.t('事中监管名额')}</span>
  //       </Space>
  //     );
  //   }

  //   if (row.key === 'accNums') {
  //     return (
  //       <Space direction="vertical" size={4}>
  //         <span className={styles.typeName}>{intl.t('事中监管名额')}</span>
  //       </Space>
  //     );
  //   }
  //   return null;
  // };

  // const columns: ColumnsType<any> = [
  //   {
  //     title: intl.t('类型'),
  //     dataIndex: 'name',
  //     //  width: 230,
  //     className: styles.typeHeader,
  //     render: (value, record) => {
  //       return <div style={{ minWidth: 140 }}>{getNameEl(record, store.VIPBuyType)}</div>;
  //     },
  //   },
  //   {
  //     title: intl.t('加购单价/{slot0}天', { slot0: selectedPackage?.period || 30 }),
  //     dataIndex: 'unitPrice',
  //     //  width: 230,
  //     className: styles.unitPriceHeader,
  //     render: (val, record) => {
  //       return (
  //         <Space>
  //           <span className={styles.discountPrice}>
  //             {globalCurrency}
  //             {toFixed(multiply(Number(record?.discountPrice), store.addSeatMultiple))}
  //             {record.key !== 'rollingStorageNums' && <span>/{intl.t('个')}</span>}
  //           </span>
  //           <span className={styles.price}>
  //             {globalCurrency}
  //             {toFixed(multiply(Number(record?.price), store.addSeatMultiple))}
  //           </span>
  //         </Space>
  //       );
  //     },
  //   },
  //   {
  //     //   width: 260,
  //     title: intl.t('加购'),
  //     dataIndex: 'addNums',
  //     render: (val, record: any) => {
  //       return (
  //         <Space style={{ flexWrap: 'nowrap', textWrap: 'nowrap' }}>
  //           <Tooltip
  //             open={predictAddNumTipOpen}
  //             placement="bottom"
  //             getPopupContainer={(triggerNode) => triggerNode.parentElement as any}
  //             destroyTooltipOnHide
  //             zIndex={9}
  //             title={
  //               <Row align="middle" wrap={false}>
  //                 <div style={{ maxWidth: 230, textWrap: 'wrap' }}>
  //                   {store.VIPBuyType === VIPBuyType.Renew ? (
  //                     <span>
  //                       {intl.t('根据您的企业成员和平台账号数量测算，建议您加购至')}
  //                       {record?.predictAddNum}
  //                       {intl.t('个名额')}
  //                     </span>
  //                   ) : (
  //                     <span>
  //                       {intl.t('根据您的企业成员和平台账号数量测算，建议您加购')}
  //                       {record?.predictAddNum}
  //                       {intl.t('个名额')}
  //                     </span>
  //                   )}
  //                   <Button
  //                     ghost
  //                     type="link"
  //                     size="small"
  //                     onClick={() => {
  //                       handlePriceSourceChange(record?.key, record?.predictAddNum || 0);
  //                       setPredictAddNumTipOpen(false);
  //                     }}
  //                     style={{ textDecoration: 'underline' }}
  //                   >
  //                     {intl.t('一键调整')}
  //                   </Button>
  //                 </div>
  //                 <Button
  //                   icon={<CloseOutlined style={{ fontSize: 14, color: 'white' }} />}
  //                   type="text"
  //                   size="small"
  //                   style={{ padding: 0 }}
  //                   onClick={() => {
  //                     setPredictAddNumTipOpen(false);
  //                   }}
  //                 />
  //               </Row>
  //             }
  //           >
  //             <InputNumber
  //               value={record.nums || null}
  //               min={record?.min}
  //               max={record?.max}
  //               step={record?.step}
  //               onChange={(value) => {
  //                 handlePriceSourceChange(record?.key, parseInt(value) || 0);
  //               }}
  //             />
  //           </Tooltip>
  //         </Space>
  //       );
  //     },
  //   },
  //   {
  //     title: intl.t('价格'),
  //     dataIndex: 'price',
  //     className: styles.priceHeader,
  //     render: (val, record) => {
  //       return record?.totalPrice ? (
  //         <div style={{ minWidth: 60 }} className={styles.discountPrice}>
  //           {globalCurrency}
  //           {record?.totalPrice}
  //         </div>
  //       ) : (
  //         <div style={{ minWidth: 60 }}>--</div>
  //       );
  //     },
  //   },
  // ];
  return (
    <div className={styles.expanPackage}>
      <div className={styles.titleWrp}>
        <span className={styles.title}>超值加购</span>
      </div>

      <div className={styles.table}>
        {dataSource.map((item) => (
          <div className={styles.item} key={item.key}>
            <div className={styles.rigthWrp}>
              <div className={styles.name}>{item.name}</div>
              <div className={styles.priceWrp}>
                <div className={styles.discountPriceWrp}>
                  <div className={styles.rmb}>{globalCurrency}</div>
                  <div className={styles.discountPrice}>{toFixed(multiply(Number(item?.discountPrice), store.addSeatMultiple) || 0)?.toFixed(2)}</div>
                </div>
                <div className={styles.price}>{globalCurrency}{toFixed(multiply(Number(item?.price), store.addSeatMultiple) || 0)?.toFixed(2)}</div>
                <div className={styles.unit}>/个</div>
              </div>
            </div>
            <div className={styles.stepperWrp}>
              <Stepper
                value={item.nums || null}
                allowEmpty={true}
                digits={0}
                min={item.min}
                max={item.max}
                step={item.step}
                onChange={(value) => {
                  handlePriceSourceChange(item?.key as any, value || 0);
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default observer(ExpandPackage);
