import _ from 'lodash';
import { observable, action, computed, makeObservable, runInAction } from 'mobx';
import {
  ACCOUNT_MODULE,
  CLOUD_PHONE_MODULE,
  DEVICE_MODULE,
  ENT_MODULE,
  FEE_MODULE,
  MEMBER_MODULE,
} from '@/components/super-permission/config';
import { RoleType } from '@/types';
import { userService } from '@/services';
import { to } from '@/utils';

const hasPermission = (codes: string[]) => (code: string) => {
  return codes?.includes(code);
};

class AuthStore {
  /**
   * @description 当前用户角色相关信息
   */

  @observable userRoleInfo?: UserService.PermissionInfo;

  constructor() {
    makeObservable(this);
  }

  @computed get authInited() {
    return !!this?.userRoleInfo;
  }

  @computed get permissionCodes() {
    return _.map(this.userRoleInfo?.permissions, (item) => item?.per_tag);
  }

  @computed get allPermissionsName() {
    return _.map(this.userRoleInfo?.permissions, (item) => item?.name);
  }

  /**
   * @description 经理
   */

  @computed
  get isManager() {
    return this.userRoleInfo?.identity_id === RoleType.Manager;
  }

  /**
   * @description 是普通员工
   */

  @computed
  get isPublicUser() {
    // return true;
    return this.userRoleInfo?.identity_id === RoleType.Staff;
  }

  /* ui相关配置 包含是否显示一级菜单逻辑 */
  @computed get UIMenuConfig() {
    return {
      /** 账号管理模块路由 */
      showAccountManageModule: this.accountManageModuleAuth?.hasCheckAuth,
      /** 设备管理模块路由 */
      showDeviceManageModule: this.deviceManageModuleAuth?.hasDeviceListAuth,
      /** 云号管理模块路由 */
      showCloudPhoneModule:
        this.cloudNumberModuleAuth?.hasCheckAuth ||
        this.cloudNumberModuleAuth?.hasManageAuth ||
        this.cloudNumberModuleAuth?.hasRecordAuth ||
        this.cloudNumberModuleAuth?.hasUnneededVerificationAuth,
      /* 企业管理模块 */
      showCompanyManageModule:
        this.companyManageModuleAuth.hasAppManageAuth ||
        this.companyManageModuleAuth.hasDepartManageAuth ||
        this.companyManageModuleAuth.hasMemberManageAuth ||
        this.companyManageModuleAuth.hasRoleManageAuth ||
        this.companyManageModuleAuth.hasEntDeployCodeAuth,
      /* 安全中心模块路由，模块包含一个权限外的访问网址加白功能 */
      showSafeCenterModule:
        this.safeCenterModuleAuth.hasEnterpriseLogAuth ||
        this.safeCenterModuleAuth.hasLoginControlAuth ||
        this.safeCenterModuleAuth.hasAppManageAuth ||
        this.safeCenterModuleAuth.hasEntPermissionLogAuth ||
        this.companyManageModuleAuth?.hasEntVisitUrlWhiteList,
      /* 费用管理模块路由 */
      showFeeManageModule:
        this.feeManageModuleAuth.hasRechargeAuth ||
        this.feeManageModuleAuth.hasBalanceDetailAuth ||
        this.feeManageModuleAuth.hasFeeListMenuAuth ||
        this.feeManageModuleAuth.hasPayServiceAuth ||
        this.feeManageModuleAuth?.remoteServiceMenuAuth,
    };
  }

  /** 账号模块权限 */
  @computed get accountManageModuleAuth() {
    const hasCheckAuth = _.some([ACCOUNT_MODULE.ACCT_CHECK], hasPermission(this.permissionCodes));
    /** 账号授权成员权限 */
    const hasAcctAuth = _.some([ACCOUNT_MODULE.ACCT_AUTH], hasPermission(this.permissionCodes));
    const hasAccountDeleteAuth = _.some(
      [ACCOUNT_MODULE.ACCT_DELETE],
      hasPermission(this.permissionCodes)
    );

    return {
      /** 添加账号权限 */
      hasAddAccountAuth: this.hasAddAccountAuth,
      /** 账号管理权限 */
      hasAccountManageAuth: this.hasAccountManageAuth,
      /** 查看账号列表权限 */
      hasCheckAuth,
      /** 删除账号 */
      hasAccountDeleteAuth,
      /** 清空所有缓存权限 */
      hasClearAllCacheAuth: this.hasClearAllCacheAuth,
      /** cookie管理权限 */
      cookieManage: this.hasAccountManageAuth || this.hasClearAllCacheAuth,
      /**账号授权*/
      hasAcctAuth,
    };
  }

  /* 企业管理模块权限 */
  @computed get companyManageModuleAuth() {
    const hasAppManageAuth = _.some(
      [ACCOUNT_MODULE.ACCT_ADD_PLUGIN, ENT_MODULE.ENT_APP_MANAGE],
      hasPermission(this.permissionCodes)
    );

    const hasMemberManageAuth = _.some(
      [MEMBER_MODULE.DMEE_EE_CHECK, MEMBER_MODULE.DMEE_EE_MANAGE],
      hasPermission(this.permissionCodes)
    );
    const hasManageAuth = _.some(
      [MEMBER_MODULE.DMEE_EE_MANAGE],
      hasPermission(this.permissionCodes)
    );
    const hasRoleManageAuth = _.some(
      [ENT_MODULE.ENT_ROLE_MANAGE],
      hasPermission(this.permissionCodes)
    );
    const hasDepartManageAuth = _.some(
      [MEMBER_MODULE.DMEE_DM_MANAGE],
      hasPermission(this.permissionCodes)
    );
    const hasClientWhiteList = _.some(
      [ENT_MODULE.ENT_TERMINAL_WL],
      hasPermission(this.permissionCodes)
    );
    const hasEntVisitUrlWhiteList =
      _.some([ENT_MODULE.ENT_VISIT_URL_WL], hasPermission(this.permissionCodes)) &&
      !!this.userRoleInfo?.is_company_whitelist;
    const hasEntDeployCodeAuth = _.some(
      [ENT_MODULE.ENT_DEPLOY_CODE],
      hasPermission(this.permissionCodes)
    );

    return {
      hasManageAuth,
      hasAppManageAuth,
      hasMemberManageAuth,
      hasRoleManageAuth,
      hasDepartManageAuth,
      hasClientWhiteList,
      hasEntVisitUrlWhiteList,
      hasEntDeployCodeAuth,
    };
  }

  /** 设备管理模块权限 */
  @computed get deviceManageModuleAuth() {
    const hasDeviceListAuth = _.some(
      [DEVICE_MODULE.DEV_MANAGE, DEVICE_MODULE.DEV_CHECK],
      hasPermission(this.permissionCodes)
    );
    const hasDeviceManageAuth = _.some(
      [DEVICE_MODULE.DEV_MANAGE],
      hasPermission(this.permissionCodes)
    );
    const hasDeviceDeleteAuth = _.some(
      [DEVICE_MODULE.DEV_DELETE],
      hasPermission(this.permissionCodes)
    );

    return {
      /** 续费设备权限 */
      hasRenewDeviceAuth: this.hasRenewDeviceAuth,
      hasDeviceListAuth,
      /** 设备管理 */
      hasDeviceManageAuth,
      /**  删除设备 */
      hasDeviceDeleteAuth,
      /** 购买设备权限 */
      hasPurchaseDeviceAuth: this.hasPurchaseDeviceAuth,
      hasMoreContentAuth:
        hasDeviceManageAuth ||
        hasDeviceDeleteAuth ||
        this.hasPurchaseDeviceAuth ||
        this.hasRenewDeviceAuth,
    };
  }
  /** 有v6账号or设备菜单 */
  @computed get v6ManageMenuAuth() {
    return this.UIMenuConfig?.showAccountManageModule || this.UIMenuConfig?.showDeviceManageModule;
  }
  /* 云号管理模块权限 */
  @computed get cloudNumberModuleAuth() {
    const hasCheckAuth = _.some([CLOUD_PHONE_MODULE.CP_CHECK], hasPermission(this.permissionCodes));
    const hasManageAuth = _.some(
      [CLOUD_PHONE_MODULE.CP_MANAGE, CLOUD_PHONE_MODULE.CP_RENEW],
      hasPermission(this.permissionCodes)
    );
    const hasRecordAuth = _.some(
      [CLOUD_PHONE_MODULE.CP_CHECK_RECORD],
      hasPermission(this.permissionCodes)
    );
    const hasUnneededVerificationAuth = _.some(
      [ENT_MODULE.ENT_NO_VERIFICATION_ENO],
      hasPermission(this.permissionCodes)
    );

    return {
      hasCheckAuth,
      hasManageAuth,
      hasRecordAuth,
      /** 免验实体号 */
      hasUnneededVerificationAuth,
    };
  }

  /* 安全中心模块权限 */
  @computed get safeCenterModuleAuth() {
    const hasVisitControlAuth = _.some(
      [ENT_MODULE.ENT_ACCESS_CONTROL],
      hasPermission(this.permissionCodes)
    );
    const hasEnterpriseLogAuth = _.some(
      [ENT_MODULE.ENT_CHECK_ENT_LOG, ENT_MODULE.ENT_ACCESS_CONTROL, ENT_MODULE.ENT_PERMISSION_LOG],
      hasPermission(this.permissionCodes)
    );

    const hasLoginControlAuth = _.some(
      [MEMBER_MODULE.DMEE_EE_MANAGE, ENT_MODULE.ENT_TERMINAL_WL],
      hasPermission(this.permissionCodes)
    );
    const hasEntAccessRequestApprovalAuth = _.some(
      [ENT_MODULE.ENT_ACCESS_REQUEST_APPROVAL],
      hasPermission(this.permissionCodes)
    );

    const hasAppManageAuth = _.some(
      [ENT_MODULE.ENT_APP_MANAGE],
      hasPermission(this.permissionCodes)
    );
    const hasEntPermissionLogAuth = _.some(
      [ENT_MODULE.ENT_PERMISSION_LOG],
      hasPermission(this.permissionCodes)
    );
    const hasSuperviseTipSetAuth = _.some(
      [ENT_MODULE.ENT_SUPERVISE_TIPS_SETTING],
      hasPermission(this.permissionCodes)
    );

    return {
      /**  成员访问控制 */
      hasVisitControlAuth,
      hasEnterpriseLogAuth,
      hasLoginControlAuth,
      hasAppManageAuth,
      hasEntPermissionLogAuth,
      hasEntAccessRequestApprovalAuth,
      hasSuperviseTipSetAuth,
    };
  }

  /* 费用管理模块权限 */
  @computed get feeManageModuleAuth() {
    const remoteServiceMenuAuth = false;
    const hasRechargeAuth = _.some(
      [
        DEVICE_MODULE.DEV_ADD,
        DEVICE_MODULE.DEV_RENEW,
        CLOUD_PHONE_MODULE.CP_BUY,
        CLOUD_PHONE_MODULE.CP_RENEW,
      ],
      hasPermission(this.permissionCodes)
    );

    const hasBalanceDetailAuth =
      hasRechargeAuth ||
      _.some([FEE_MODULE.FEE_PURSE_DETAIL_EXPORT], hasPermission(this.permissionCodes));

    const hasFeeListMenuAuth =
      hasRechargeAuth ||
      _.some([FEE_MODULE.FEE_ORDER_DETAIL_EXPORT], hasPermission(this.permissionCodes));

    const hasPayServiceAuth = _.some(
      [FEE_MODULE.FEE_CP_PAID_SERVICE],
      hasPermission(this.permissionCodes)
    );
    const hasFeeBalanceWarning = _.some(
      [FEE_MODULE.FEE_BALANCE_WARN],
      hasPermission(this.permissionCodes)
    );
    return {
      /** 余额告警设置 */
      hasFeeBalanceWarning,
      hasRechargeAuth,
      hasBalanceDetailAuth,
      hasFeeListMenuAuth,
      hasPayServiceAuth,
      remoteServiceMenuAuth,
    };
  }

  /** 账号管理权限 */
  @computed get hasAccountManageAuth() {
    return _.some([ACCOUNT_MODULE.ACCT_MANAGE], hasPermission(this.permissionCodes));
  }

  /** 清空所有缓存权限 */
  @computed get hasClearAllCacheAuth() {
    return _.some([ACCOUNT_MODULE.ACCT_CLEAR_CACHE], hasPermission(this.permissionCodes));
  }

  /** 添加账号权限 */
  @computed get hasAddAccountAuth() {
    return _.some([ACCOUNT_MODULE.ACCT_ADD], hasPermission(this.permissionCodes));
  }

  /* 购买临时设备权限 */
  @computed get hasPurchaseAlternateDeviceAuth() {
    return _.some(
      [DEVICE_MODULE.DEV_ADD, DEVICE_MODULE.DEV_MANAGE],
      hasPermission(this.permissionCodes)
    );
  }

  /* 购买云号权限 */
  @computed get hasPurchaseCloudNumberAuth() {
    return _.some([CLOUD_PHONE_MODULE.CP_BUY], hasPermission(this.permissionCodes));
  }

  /* 续费云号权限 */
  @computed get hasRenewCloudNumberAuth() {
    return _.some([CLOUD_PHONE_MODULE.CP_RENEW], hasPermission(this.permissionCodes));
  }

  /** 购买设备权限 */
  @computed get hasPurchaseDeviceAuth() {
    return _.some([DEVICE_MODULE.DEV_ADD], hasPermission(this.permissionCodes));
  }

  /** VIP购买/续费权限 */
  @computed get hasVIPBuyAuth() {
    return _.some(
      [ENT_MODULE.ENT_BUY_AND_RENEW_VALUED_ADDED_SERVICE],
      hasPermission(this.permissionCodes),
    );
  }

  /** 续费设备权限 */
  @computed get hasRenewDeviceAuth() {
    return _.some([DEVICE_MODULE.DEV_RENEW], hasPermission(this.permissionCodes));
  }

  /* 拥有续费权限，云号或者设备续费权限 */
  @computed get hasRenewAuth() {
    return _.some(
      [DEVICE_MODULE.DEV_RENEW, CLOUD_PHONE_MODULE.CP_RENEW],
      hasPermission(this.permissionCodes)
    );
  }

  /* 拥有查看钱包余额的权限 */
  @computed get hasPurseBalanceAuth() {
    return this.feeManageModuleAuth?.hasBalanceDetailAuth;
  }

  /* 拥有查看信用余额的权限 */
  @computed get hasCreditBalanceAuth() {
    return this.feeManageModuleAuth?.hasBalanceDetailAuth;
  }

  /** 账号页统计栏的相关权限 */
  @computed get statisticsAuth() {
    return {
      hasPurseBalanceAuth: this.hasPurseBalanceAuth,
    };
  }

  /** 管理模块路由 */
  @computed get showManageModule() {
    const conf = this.UIMenuConfig;

    return conf.showFeeManageModule || conf.showCompanyManageModule || conf.showSafeCenterModule;
  }

  @action
  hasPermission = (code: string) => {
    return this.permissionCodes?.includes(code);
  };

  @action
  setUserRoleInfo = (info: UserService.PermissionInfo) => {
    runInAction(() => {
      this.userRoleInfo = info;
    });
  };

  async fetchUserPermission() {
    const [err, response] = await to(userService.getUserPermission());
    if (err) return;

    this.setUserRoleInfo(response!);
  }
}

export default AuthStore;
