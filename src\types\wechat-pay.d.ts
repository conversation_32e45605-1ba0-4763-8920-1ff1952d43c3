/**
 * 微信支付相关类型定义
 */

declare namespace WechatPay {
  /**
   * 微信环境检测结果
   */
  interface WechatEnvironment {
    /** 是否在微信浏览器中 */
    isWechat: boolean;
    /** 是否在企业微信中 */
    isWechatWork: boolean;
    /** 是否在微信小程序中 */
    isWechatMiniProgram: boolean;
    /** 是否在微信浏览器中（排除小程序） */
    isWechatBrowser: boolean;
    /** 用户代理字符串 */
    userAgent: string;
  }

  /**
   * 微信授权响应
   */
  interface AuthResponse {
    /** 用户openid */
    openid: string;
    /** 访问令牌 */
    access_token: string;
    /** 刷新令牌 */
    refresh_token: string;
    /** 令牌过期时间（秒） */
    expires_in: number;
    /** 授权范围 */
    scope: string;
    /** 错误码 */
    errcode?: number;
    /** 错误信息 */
    errmsg?: string;
  }

  /**
   * JSAPI支付订单创建参数
   */
  interface CreateOrderParams {
    /** 交易单号 */
    purse_detail_id: string;
    /** 支付金额（元） */
    total_fee: string;
    /** 机器字符串 */
    code: string;
    /** 匹配字符串 */
    matching_string: string;
    /** 用户ID */
    user_id: string;
  }

  /**
   * 后端API返回的支付参数
   */
  interface APIPayResponse {
    data: {
      /** 预支付交易会话标识 */
      package: string;
      /** 随机字符串 */
      nonce_str: string;
      /** 时间戳 */
      timestamp: string;
      /** 签名方式 */
      sign_type: string;
      /** 签名 */
      pay_sign: string;
    };
    /** 返回状态码 */
    ret: string;
    /** 返回信息 */
    msg: string;
  }

  /**
   * JSAPI支付参数
   */
  interface JSAPIPayParams {
    /** 公众号ID */
    appId: string;
    /** 时间戳 */
    timeStamp: string;
    /** 随机字符串 */
    nonceStr: string;
    /** 订单详情扩展字符串 */
    package: string;
    /** 签名方式 */
    signType: string;
    /** 签名 */
    paySign: string;
  }

  /**
   * 支付结果
   */
  interface PayResult {
    /** 错误信息 */
    errMsg: string;
    /** 结果代码 */
    resultCode?: 'SUCCESS' | 'CANCEL' | 'FAIL';
    /** 原始响应 */
    rawResponse?: any;
  }

  /**
   * 订单状态查询响应
   */
  interface OrderStatusResponse {
    /** 交易状态 */
    trade_state: 'SUCCESS' | 'REFUND' | 'NOTPAY' | 'CLOSED' | 'REVOKED' | 'USERPAYING' | 'PAYERROR';
    /** 交易状态描述 */
    trade_state_desc: string;
    /** 商户订单号 */
    out_trade_no: string;
    /** 微信支付订单号 */
    transaction_id?: string;
  }

  /**
   * 充值数据
   */
  interface RechargeData {
    /** 充值订单ID */
    purse_detail_id: string;
    /** 充值金额（元） */
    total_fee: string;
    /** 用户openid */
    openid: string;
  }

  /**
   * 充值结果
   */
  interface RechargeResult {
    /** 是否成功 */
    success: boolean;
    /** 结果信息 */
    message: string;
    /** 是否取消 */
    cancelled?: boolean;
    /** 订单信息 */
    orderInfo?: {
      purse_detail_id: string;
      total_fee: string;
      description: string;
    };
    /** 订单状态 */
    orderStatus?: OrderStatusResponse;
    /** 错误对象 */
    error?: Error;
  }

  /**
   * 充值历史记录
   */
  interface RechargeHistory {
    /** 记录列表 */
    list: Array<{
      /** 充值订单ID */
      purse_detail_id: string;
      /** 充值金额 */
      amount: string;
      /** 状态 */
      status: string;
      /** 创建时间 */
      created_at: string;
      /** 支付时间 */
      pay_time?: string;
      /** 交易号 */
      trade_no?: string;
    }>;
    /** 总数 */
    total: number;
    /** 页码 */
    page: number;
    /** 每页数量 */
    limit: number;
  }

  /**
   * 支付回调函数类型
   */
  interface PaymentCallbacks {
    /** 支付成功回调 */
    onSuccess?: (result: any) => void;
    /** 支付失败回调 */
    onError?: (error: Error) => void;
    /** 支付取消回调 */
    onCancel?: () => void;
    /** 重试回调 */
    onRetry?: () => void;
    /** 余额更新回调 */
    onBalanceUpdate?: () => void;
  }

  /**
   * 微信授权配置
   */
  interface AuthConfig {
    /** 公众号AppId */
    appId: string;
    /** 重定向URI */
    redirectUri: string;
    /** 授权范围 */
    scope?: 'snsapi_base' | 'snsapi_userinfo';
    /** 状态参数 */
    state?: string;
  }

  /**
   * 支付参数验证结果
   */
  interface PayParamsValidation {
    /** 是否有效 */
    isValid: boolean;
    /** 错误信息列表 */
    errors: string[];
  }

  /**
   * 金额验证结果
   */
  interface AmountValidation {
    /** 是否有效 */
    isValid: boolean;
    /** 错误信息 */
    error?: string;
  }

  /**
   * 微信支付服务接口
   */
  interface WechatPayService {
    /** 获取微信授权URL */
    getWechatAuthUrl(redirectUri: string, state?: string): string;
    /** 通过授权码获取openid */
    getOpenIdByCode(code: string): Promise<AuthResponse>;
    /** 创建JSAPI支付订单 */
    createJSAPIOrder(orderData: CreateOrderParams): Promise<JSAPIPayParams>;
    /** 调起微信支付 */
    invokeWechatPay(payParams: JSAPIPayParams): Promise<PayResult>;
    /** 查询订单状态 */
    queryOrderStatus(outTradeNo: string): Promise<OrderStatusResponse>;
    /** 检查是否在微信浏览器中 */
    isWechatBrowser(): boolean;
  }

  /**
   * 微信充值支付服务接口
   */
  interface WechatRechargePayService {
    /** 创建充值订单 */
    createRechargeOrder(rechargeData: RechargeData): Promise<JSAPIPayParams & { orderInfo: any }>;
    /** 执行充值支付 */
    executeRechargePay(rechargeData: RechargeData): Promise<RechargeResult>;
    /** 查询充值订单状态 */
    queryRechargeOrderStatus(purseDetailId: string): Promise<OrderStatusResponse>;
    /** 获取充值历史 */
    getRechargeHistory(params?: any): Promise<RechargeHistory>;
    /** 验证充值金额 */
    validateRechargeAmount(amount: string | number): AmountValidation;
    /** 格式化充值金额 */
    formatRechargeAmount(amount: string | number): string;
  }

  /**
   * 微信授权组件Props
   */
  interface WechatAuthProps {
    /** 授权成功回调 */
    onAuthSuccess?: (openid: string) => void;
    /** 授权失败回调 */
    onAuthError?: (error: Error) => void;
    /** 子组件 */
    children?: React.ReactNode;
  }

  /**
   * 微信支付组件Props
   */
  interface WechatPayProps {
    /** 订单信息 */
    orderInfo: {
      purse_detail_id?: string;
      total_fee?: string;
      out_trade_no?: string;
      description?: string;
      amount?: number;
      attach?: string;
    };
    /** 支付类型 */
    paymentType?: 'recharge' | 'general';
    /** 支付成功回调 */
    onPaySuccess?: (result: any) => void;
    /** 支付失败回调 */
    onPayError?: (error: Error) => void;
    /** 支付取消回调 */
    onPayCancel?: () => void;
    /** 按钮文本 */
    buttonText?: string;
    /** 是否禁用 */
    disabled?: boolean;
    /** 按钮类型 */
    buttonType?: 'primary' | 'default';
    /** 是否块级按钮 */
    block?: boolean;
    /** 自定义样式类名 */
    className?: string;
  }

  /**
   * 微信授权Hook返回值
   */
  interface UseWechatAuthReturn {
    /** 用户openid */
    openid: string;
    /** 是否正在授权 */
    isAuthorizing: boolean;
    /** 开始授权 */
    startAuth: () => Promise<string>;
    /** 清除授权 */
    clearAuth: () => void;
    /** 是否已授权 */
    isAuthorized: boolean;
  }

  /**
   * 微信支付Hook返回值
   */
  interface UseWechatPayReturn {
    /** 开始支付 */
    startPayment: (orderInfo: any, paymentType?: 'recharge' | 'general') => Promise<any>;
    /** 是否正在支付 */
    isPaymentLoading: boolean;
    /** 是否已授权 */
    isAuthorized: boolean;
  }
}

/**
 * 扩展Window接口以支持微信JS-SDK
 */
declare global {
  interface Window {
    /** 微信JS桥接对象 */
    WeixinJSBridge: {
      invoke: (method: string, params: any, callback: (res: any) => void) => void;
      on: (event: string, callback: () => void) => void;
    };
    /** 微信小程序环境标识 */
    __wxjs_environment?: string;
  }
}

export = WechatPay;
export as namespace WechatPay;
