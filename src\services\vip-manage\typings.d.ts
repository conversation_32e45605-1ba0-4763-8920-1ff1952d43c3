
declare namespace VipAPI {
  interface Equity {
    review_day: number;
    member: number;
    account: number;
    policy_customization_time: number;
    security_consultant: number;
    local_device_coupon: number;
    remote_duration_free_min: number;
    replace_time: {
      [key: string]: number;
    };
  }

  interface Package {
    id: number;
    name: string;
    package_type: number;
    sort: number;
    price: number;
    period: number;
    discount_status: number;
    discount_price: number;
    discount_starttime: number;
    discount_endtime: number;
    remark: string;
    equity: Equity;
    supervise_type: SuperviseType;
  }
  interface VipCenterBaseDetail {
    effect: VipCenterBaseDetailList[],
    invalid: VipCenterBaseDetailList[]
  }
  interface VipCenterBaseDetailList {
    period_start: number
    period_end: number
    period_start_str: string
    period_end_str: string
    status: 0 | 1
    type: 1 | 2	//1会员套餐数据 2加购数据	
  }
  interface ReviewDetailList extends VipCenterBaseDetailList {
    supervise_day: number;
  }
  interface MemberDetailList extends VipCenterBaseDetailList {
    supervise_user_num: number;
  }
  interface AccountDetailList extends VipCenterBaseDetailList {
    supervise_account_num: number;
  }
  interface ReviewDetail {
    effect: ReviewDetailList[],
    invalid: ReviewDetailList[]
  }
  interface MemberDetail {
    effect: MemberDetailList[],
    invalid: MemberDetailList[]
  }
  interface AccountDetail {
    effect: AccountDetailList[],
    invalid: AccountDetailList[]
  }
  interface UsedBenefit {
    /**已使用平台账号数 */
    account_count: number
    //已使用用户数	
    user_count: number
    //已使用远程时长数	
    remote_time: number
    //已使用优惠券数	
    coupon_count: number
    //已使用设备替换数
    device_replace_count: number
  }
  /**图片存储开通会员弹窗 */
  interface ReviewInfo {
    last_view_time: number;
    last_view_user: {
      name: string,
      username: string
    };
  }
  interface ExpandPackage {
    /** 套餐id */
    id?: number;
  
    /** 套餐名 */
    name?: string;
    /** 累加数量 */
    step?: number;
    /** 套餐说明 */
    remark: string,
    /** 单价	*/
    price: number,
    /** 折扣单价	*/
    discount_price: number,
    /** 选项	*/
    option: {
      /** 类型  1固定模式   2范围模式 */
      type: 1 | 2,
      /** 范围模式最小值 */
      min: number,
      /** 范围模式最大值 */
      max: number,
      /** 固定模式选项 */
      options?: {
        /** 选项名称 */
        name: string,
        /** 步数	 */
        step: number
      },
    }
  }
  interface VipPackage {
    supervise_type: SuperviseType;
    /** 套餐id */
    id: number;
    /** 套餐类型 0免费套餐 1会员套餐	 */
    package_type?: PackageType;
    /** 套餐名 */
    name: string;
    /** 单价	*/
    price: number,
    /** 套餐时长 */
    period: number;
    /** 套餐折扣状态 0 无折扣 1需要判断折扣时间 2可使用折扣*/
    discount_status: 2 | 1 | 0;
    /** 套餐折扣价格 */
    discount_price: number;
    /** 套餐折扣开始 */
    discount_starttime: number;
    /** 套餐折扣结束 */
    discount_endtime: number;
    /** 套餐折扣 */
    discount: number;
    /** 套餐说明 */
    remark: string;
    /** 权益信息	*/
    equity: {
      /** 滚动存储时长 */
      review_day: number;
      /** 名额数量 */
      seat: number
      // /** 监管成员数量 */
      // member: number;
      // /** 监管平台账号数量 */
      // account: number;
      /** 免费定制访问策略次数 */
      policy_customization_time: number;
      /** 专属安全顾问  1是 0否 */
      security_consultant: 1 | 0;
      /** 免费本地设备数量 */
      local_device_coupon: number;
      /** 免费远程访问时长 */
      remote_duration_free_min: number;
      /** 自助替换设备数量 */
      replace_time: {
        0: number,
        6: number,
        12: number
      }
    }
  }
}


