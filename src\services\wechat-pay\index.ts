import { httpService } from '@/apis';
import { to } from '@/utils';

/**
 * 微信公众号支付服务类
 * 基于 /purse/wechatpay_js 接口实现JSAPI支付完整流程
 */
class WechatJSAPIPayService {
  /**
   * 获取微信公众号授权URL
   * @param redirectUri 授权后重定向的URI
   * @param state 自定义参数，用于防止CSRF攻击
   * @returns 授权URL
   */
  getWechatAuthUrl(redirectUri: string, state?: string): string {
    const appId = this.getWechatAppId();
    const encodedRedirectUri = encodeURIComponent(redirectUri);
    const scope = 'snsapi_base'; // 静默授权，只获取openid
    const stateParam = state || Date.now().toString();

    return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${encodedRedirectUri}&response_type=code&scope=${scope}&state=${stateParam}#wechat_redirect`;
  }

  /**
   * 通过授权码获取用户openid
   * @param code 微信授权码
   * @returns Promise<{openid: string, access_token: string}>
   */
  async getOpenIdByCode(code: string) {
    // 参数验证
    if (!code || typeof code !== 'string') {
      throw new Error('授权码无效');
    }

    if (code.length < 10) {
      throw new Error('授权码格式错误');
    }

    const [err, res] = await to(
      httpService<{
        openid: string;
        access_token: string;
        refresh_token: string;
        expires_in: number;
        scope: string;
        errcode?: number;
        errmsg?: string;
      }>({
        url: '/wechat/oauth2/access_token',
        method: 'POST',
        data: { code },
        timeout: 10000, // 10秒超时
      })
    );

    if (err) {
      throw new Error('获取用户授权失败，请重试');
    }

    // 检查微信API返回的错误码
    if (res?.errcode) {
      const errorMessages: Record<number, string> = {
        40013: '授权码无效，请重新授权',
        40029: '授权码已过期，请重新授权',
        41008: 'AppID参数错误',
        41004: 'AppSecret参数错误',
        40001: 'AppSecret错误或者AppID无效',
        40002: '不合法的凭证类型',
        40003: '不合法的OpenID',
      };

      const errorMsg = errorMessages[res.errcode] || res.errmsg || '微信授权失败';
      throw new Error(errorMsg);
    }

    // 验证返回数据
    if (!res?.openid) {
      throw new Error('获取用户信息失败，请重新授权');
    }

    return res;
  }

  /**
   * 创建微信JSAPI充值订单
   * 使用 /purse/wechatpay_js 接口
   * @param orderData 充值订单数据
   * @returns Promise<支付参数>
   */
  async createRechargeOrder(orderData: {
    purse_detail_id: string;
    total_fee: string;
    code: string;
    matching_string: string;
    user_id: string;
  }) {
    // 参数验证
    const validation = this.validateJSAPIOrderData(orderData);
    if (!validation.isValid) {
      throw new Error(`订单参数错误: ${validation.errors.join(', ')}`);
    }

    const [err, res] = await to(
      httpService<{
        data: {
          package: string;
          nonce_str: string;
          timestamp: string;
          sign_type: string;
          pay_sign: string;
        };
        ret: string;
        msg: string;
      }>({
        url: '/purse/wechatpay_js_dynamic',
        method: 'POST',
        data: orderData,
        timeout: 15000, // 15秒超时
      })
    );

    if (err) {
      throw new Error('创建支付订单失败，请重试');
    }

    // 转换为微信支付所需的格式
    const payParams = {
      // appId: this.getWechatAppId(),
      appId: res.appid,
      timeStamp: res.data.timestamp,
      nonceStr: res.data.nonce_str,
      package: res.data.package,
      signType: res.data.sign_type,
      paySign: res.data.pay_sign,
    };

    // 验证返回的支付参数
    const payParamsValidation = this.validatePayParams(payParams);
    if (!payParamsValidation.isValid) {
      throw new Error('服务器返回的支付参数无效');
    }

    return payParams;
  }

  /**
   * 创建微信JSAPI订单支付
   * 使用 /order/wechat_js_pay 接口
   * @param orderData 订单支付数据
   * @returns Promise<支付参数>
   */
  async createOrderPayment(orderData: {
    trade_no: string; // 根据接口文档，订单ID字段是trade_no
    total_fee: string;
    openid: string;
    code?: string; // 微信授权code，传参code和openid二选一
    matching_string?: string; // 机器字符串
    user_id?: string; // 用户ID
    [key: string]: any; // 允许其他可选参数
  }) {
    // 参数验证
    const validation = this.validateOrderPaymentData(orderData);
    if (!validation.isValid) {
      throw new Error(`订单参数错误: ${validation.errors.join(', ')}`);
    }

    const [err, res] = await to(
      httpService<{
        data: {
          package: string;
          nonce_str: string;
          timestamp: string;
          sign_type: string;
          pay_sign: string;
        };
        ret: string;
        msg: string;
      }>({
        url: '/order/wechat_js_pay',
        method: 'POST',
        data: orderData,
        timeout: 15000, // 15秒超时
      })
    );

    if (err) {
      throw new Error('创建支付订单失败，请重试');
    }

    // 转换为微信支付所需的格式
    const payParams = {
      // appId: this.getWechatAppId(),
      appId: res.appid,
      timeStamp: res.timestamp,
      nonceStr: res.nonce_str,
      package: res.package,
      signType: res.sign_type,
      paySign: res.pay_sign,
    };

    // 验证返回的支付参数
    const payParamsValidation = this.validatePayParams(payParams);
    if (!payParamsValidation.isValid) {
      throw new Error('服务器返回的支付参数无效');
    }

    return payParams;
  }

  /**
   * 调起微信支付
   * @param payParams 支付参数
   * @returns Promise<支付结果>
   */
  async invokeWechatPay(payParams: {
    appId: string;
    timeStamp: string;
    nonceStr: string;
    package: string;
    signType: string;
    paySign: string;
  }): Promise<{
    errMsg: string;
    resultCode?: string;
  }> {
    return new Promise((resolve, reject) => {
      // 检查是否在微信浏览器中
      if (!this.isWechatBrowser()) {
        reject(new Error('请在微信中打开'));
        return;
      }

      // 验证支付参数
      const validation = this.validatePayParams(payParams);
      if (!validation.isValid) {
        reject(new Error(`支付参数错误: ${validation.errors.join(', ')}`));
        return;
      }

      // 设置超时机制
      const timeout = setTimeout(() => {
        reject(new Error('微信支付初始化超时，请重试'));
      }, 10000); // 10秒超时

      const cleanup = () => {
        clearTimeout(timeout);
      };

      // 检查微信JS-SDK是否已加载
      if (typeof window.WeixinJSBridge === 'undefined') {
        let readyHandlerAdded = false;

        const handleReady = () => {
          cleanup();
          this.callWechatPay(payParams, resolve, reject);
        };

        if (document.addEventListener) {
          document.addEventListener('WeixinJSBridgeReady', handleReady, { once: true });
          readyHandlerAdded = true;
        } else if ((document as any).attachEvent) {
          (document as any).attachEvent('WeixinJSBridgeReady', handleReady);
          (document as any).attachEvent('onWeixinJSBridgeReady', handleReady);
          readyHandlerAdded = true;
        }

        // 如果无法添加事件监听器，直接失败
        if (!readyHandlerAdded) {
          cleanup();
          reject(new Error('无法初始化微信支付环境'));
        }
      } else {
        cleanup();
        this.callWechatPay(payParams, resolve, reject);
      }
    });
  }

  /**
   * 调用微信支付
   * @param payParams 支付参数
   * @param resolve Promise resolve
   * @param reject Promise reject
   */
  private callWechatPay(
    payParams: any,
    resolve: (value: any) => void,
    reject: (reason?: any) => void
  ) {
    try {
      // 设置支付超时
      const payTimeout = setTimeout(() => {
        reject(new Error('支付超时，请重试'));
      }, 60000); // 60秒支付超时

      window.WeixinJSBridge.invoke(
        'getBrandWCPayRequest',
        {
          appId: payParams.appId,
          timeStamp: payParams.timeStamp,
          nonceStr: payParams.nonceStr,
          package: payParams.package,
          signType: payParams.signType,
          paySign: payParams.paySign,
        },
        (res: any) => {
          clearTimeout(payTimeout);

          try {
            if (res.err_msg === 'get_brand_wcpay_request:ok') {
              // 支付成功
              resolve({
                errMsg: 'requestPayment:ok',
                resultCode: 'SUCCESS',
                rawResponse: res,
              });
            } else if (res.err_msg === 'get_brand_wcpay_request:cancel') {
              // 用户取消支付
              resolve({
                errMsg: 'requestPayment:cancel',
                resultCode: 'CANCEL',
                rawResponse: res,
              });
            } else {
              // 支付失败
              const errorMsg = this.getPaymentErrorMessage(res.err_msg);
              reject(new Error(errorMsg));
            }
          } catch (error) {
            reject(new Error('支付结果处理异常'));
          }
        }
      );
    } catch (error) {
      reject(new Error('调起微信支付失败'));
    }
  }

  /**
   * 查询充值订单状态
   * 使用 /purse/check 接口
   * @param purseDetailId 充值订单ID
   * @returns Promise<充值订单状态>
   */
  async queryRechargeStatus(purseDetailId: string) {
    const [err, res] = await to(
      httpService<{
        trade_state: string;
        trade_state_desc: string;
        purse_detail_id: string;
        transaction_id?: string;
        pay_time?: string;
      }>({
        url: '/purse/check',
        method: 'POST',
        data: { purse_detail_id: purseDetailId },
      })
    );

    if (err) {
      throw new Error('查询充值状态失败');
    }

    return res;
  }

  /**
   * 查询订单支付状态
   * 使用 /order/check 接口
   * @param orderId 订单ID
   * @returns Promise<订单支付状态>
   */
  async queryOrderPaymentStatus(orderId: string) {
    const [err, res] = await to(
      httpService<{
        trade_state: string;
        trade_state_desc: string;
        order_id: string;
        transaction_id?: string;
        pay_time?: string;
      }>({
        url: '/order/check',
        method: 'POST',
        data: { order_id: orderId },
      })
    );

    if (err) {
      throw new Error('查询订单支付状态失败');
    }

    return res;
  }

  /**
   * 检查是否在微信浏览器中
   * @returns boolean
   */
  isWechatBrowser(): boolean {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('micromessenger');
  }

  /**
   * 获取微信公众号AppId
   * @returns string
   */
  private getWechatAppId(): string {
    // 优先从环境变量获取
    const appId = import.meta.env.VITE_WECHAT_APP_ID;
    if (appId && appId !== 'your_wechat_app_id') {
      return appId;
    }

    // 从全局配置获取
    if (typeof window !== 'undefined' && (window as any).__WECHAT_CONFIG__?.appId) {
      return (window as any).__WECHAT_CONFIG__.appId;
    }

    // 如果都没有配置，抛出错误而不是返回默认值
    throw new Error('微信公众号AppId未配置，请检查环境变量VITE_WECHAT_APP_ID');
  }

  /**
   * 验证JSAPI订单数据
   * @param orderData 订单数据
   * @returns 验证结果
   */
  private validateJSAPIOrderData(orderData: {
    purse_detail_id: string;
    total_fee: string;
    code: string;
    matching_string: string;
    user_id: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!orderData.purse_detail_id) {
      errors.push('缺少交易单号');
    }

    if (!orderData.total_fee) {
      errors.push('缺少支付金额');
    } else {
      const amount = parseFloat(orderData.total_fee);
      if (isNaN(amount) || amount <= 0) {
        errors.push('支付金额无效');
      }
    }

    if (!orderData.code) {
      errors.push('缺少机器字符串');
    }

    if (!orderData.matching_string) {
      errors.push('缺少匹配字符串');
    }

    if (!orderData.user_id) {
      errors.push('缺少用户ID');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证订单支付数据
   * @param orderData 订单支付数据
   * @returns 验证结果
   */
  private validateOrderPaymentData(orderData: {
    trade_no: string;
    total_fee: string;
    openid: string;
    [key: string]: any;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!orderData.trade_no) {
      errors.push('缺少交易号(trade_no)');
    }

    if (!orderData.total_fee) {
      errors.push('缺少支付金额');
    } else {
      const amount = parseFloat(orderData.total_fee);
      if (isNaN(amount) || amount <= 0) {
        errors.push('支付金额无效');
      }
    }

    if (!orderData.openid) {
      errors.push('缺少用户OpenID');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 验证支付参数
   * @param params 支付参数
   * @returns 验证结果
   */
  private validatePayParams(params: {
    appId?: string;
    timeStamp?: string;
    nonceStr?: string;
    package?: string;
    signType?: string;
    paySign?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!params.appId) {
      errors.push('缺少appId参数');
    }

    if (!params.timeStamp) {
      errors.push('缺少timeStamp参数');
    } else if (!/^\d+$/.test(params.timeStamp)) {
      errors.push('timeStamp参数格式错误');
    }

    if (!params.nonceStr) {
      errors.push('缺少nonceStr参数');
    } else if (params.nonceStr.length < 1 || params.nonceStr.length > 32) {
      errors.push('nonceStr参数长度错误');
    }

    if (!params.package) {
      errors.push('缺少package参数');
    } else if (!params.package.startsWith('prepay_id=')) {
      errors.push('package参数格式错误');
    }

    if (!params.signType) {
      errors.push('缺少signType参数');
    } else if (!['MD5', 'HMAC-SHA256', 'RSA'].includes(params.signType)) {
      errors.push('signType参数值无效');
    }

    if (!params.paySign) {
      errors.push('缺少paySign参数');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 获取支付错误信息
   * @param errMsg 错误消息
   * @returns 用户友好的错误信息
   */
  private getPaymentErrorMessage(errMsg: string): string {
    const errorMessages: Record<string, string> = {
      'get_brand_wcpay_request:fail': '支付失败，请重试',
      'get_brand_wcpay_request:timeout': '支付超时，请重试',
      'get_brand_wcpay_request:system_error': '系统错误，请稍后重试',
      'get_brand_wcpay_request:invalid_params': '支付参数错误',
      'get_brand_wcpay_request:not_support': '当前环境不支持微信支付',
    };

    return errorMessages[errMsg] || `支付失败: ${errMsg}`;
  }
}

// 导出单例
export const wechatJSAPIPayService = new WechatJSAPIPayService();

// 扩展Window接口以支持微信JS-SDK
declare global {
  interface Window {
    WeixinJSBridge: {
      invoke: (method: string, params: any, callback: (res: any) => void) => void;
      on: (event: string, callback: () => void) => void;
    };
  }
}
