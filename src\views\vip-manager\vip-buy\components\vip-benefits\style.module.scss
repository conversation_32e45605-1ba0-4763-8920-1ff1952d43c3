.top {
  background-color: $white;
}


.vip-benefits {
  :global {
    .adm-popup-body {
      background: linear-gradient(180deg, #F3DDBD 26%, #F5F5F5 77%);
    }
  }
}

.body {
  display: flex;
  flex-direction: column;
  height: 80vh;
  border-radius: 8px 8px 0 0;
  background: linear-gradient(180deg, #F3DDBD 26%, #F5F5F5 77%);

  .titleWrp {
    display: flex;
    margin-top: 26px;

    .title {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .text {
        font-size: 20px;
        color: #663C0A;
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
      }

      .lineIcon {
        width: 274px;
        height: 8px;
        margin: 16px 0px 8px 0;
      }
    }

    .vipIcon {
      width: 122px;
      height: 122px;
      position: absolute;
      top: 0px;
      right: 6px;
    }
  }
}

.imgWrp {
  overflow: auto;

  .benefitsImgWrp {
    overflow: auto;
    display: flex;
    justify-content: center;

    .benefitsImg {
      width: 343px;
      position: relative;
    }
  }
}

.container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}



.device-card {
  margin-top: $margin-xss;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;
  padding: 5px 12px 0px 12px;
  background-color: $white;
}

.container-title {
  font-size: $font-size-base;
  color: $color-text-tertiary;
}

.info-box {
  padding: 6px 0;
  border-bottom: 1px solid $color-bg-gray;
}

.item-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: $font-size-base;
  margin-bottom: $margin-xss;

  .couponItem {
    font-size: 13px;
    line-height: 20px;

    .hasCoupon {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 4px 0 8px;
      background: #ff4d4f;
      color: #ffffff;
      border-radius: 10px;
    }

    .noCoupon {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .proxyName {
    width: 50%;
    word-break: break-all;
  }
}

.price-font {
  font-size: 13px;
  color: $color-text-secondary;
  margin-bottom: $margin-xs;

  &:last-child {
    margin-bottom: 0;
  }
}

.text-gray {
  font-size: $font-size-small;
  color: $color-text-tertiary;
  margin-bottom: $margin-xss;
  font-family: PingFang SC, PingFang SC;
}

.amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 46px;
}

.big-font {
  color: $black;
  font-size: $font-size-large;
  font-family: PingFang SC, PingFang SC;
  margin-left: $margin-xss;
}

.sure {
  width: 100vw;
  display: flex;
  justify-content: space-between;
  align-items: center;
  bottom: 0px;
  height: 67px;
  z-index: 2;
  background: rgba(0, 0, 0, 0.88);
  border-radius: 12px 12px 0px 0px;

  .sure-content {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    padding: 10px 8px;

    .textWrp {
      position: relative;

      .tag {
        padding: 3px 5px;
        background: #FF8A00;
        border-radius: 4px;
        font-size: 14px;
        color: white;
        position: absolute;
        top: -25px;
        left: -8px;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;
      }

      .text {
        color: white;
        font-size: 14px;
        width: 140px;
      }
    }


    .btnWrp {}
  }
}

.red-font {
  color: $color-danger;
  font-size: $font-size-small;
  margin-top: $padding-xss;
}

.wePay {
  width: 20px;
  height: 24px;
}

.icon-bg {
  color: #fff;
  width: 24px;
  height: 24px;
  text-align: center;
  border-radius: 10%;
  margin-right: 10px;
}

.wx {
  background: #24b340;
}

.icon {
  width: 20px;
  height: 24px;
}

.money {
  background: linear-gradient(135deg, #ffa00f 0%, #fc620b 100%);
}

.ali {
  background-color: $white;
  color: #1677ff;
}

.ali-icon {
  width: 24px;
}

.paybox {
  display: flex;
  align-items: center;
}

.pay-font {
  color: $black;
  margin-bottom: 19px;
}

.pay-font:last-child {
  margin-bottom: 10px;
}

.gray-font {
  font-size: $font-size-small;
  color: $color-text-tertiary;
}

.payBox {
  margin-top: $margin-xss;
  box-shadow: 0px 0 6px 0px rgba(0, 0, 0, 0.06);
  border-radius: $radius-small;
  padding: 10px 12px 8px;
  background-color: $white;
}

.renewBox {

  // flex: 1;
  // height: 0;
  height: 150px;
  padding-bottom: 10px;
}
