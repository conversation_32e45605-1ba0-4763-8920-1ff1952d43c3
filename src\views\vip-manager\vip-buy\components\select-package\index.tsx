import { observer } from 'mobx-react';
import { FC } from 'react';
import Store from '../../store';
import ExpandPackage from './expand-package';
import Package from './package';
import styles from './style.module.scss';
import OrderDetail from '../order-detail';

interface IProps {
  store: Store
}

const SelectPackage: FC<IProps> = ({ store }) => {

  return <div className={styles.vipPackage}>
    <Package store={store} />

    <ExpandPackage store={store} />

    <OrderDetail store={store} />

    {/* 
    <ExpiredTime vipBuyType={store.VIPBuyType} />
    <PayMethods store={store} /> */}
  </div>
}

export default observer(SelectPackage);
