import React, { useEffect, useState, useCallback } from 'react';
import { observer } from 'mobx-react';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils';
import ClientRouter from '@/base/client/client-router';
import HeaderNavbar from '@/components/header-navbar';
import { SearchBar, Toast, Checkbox, Button, Modal } from 'antd-mobile';
import type { CheckboxValue } from 'antd-mobile/es/components/checkbox';
import { FilterOutline } from 'antd-mobile-icons';
import SuperviseTabs from '@/views/security/components/tabs';
import VipTips from '@/views/security/components/vip-tips';
import { useRequest, useCreation, useDebounceEffect, useMemoizedFn } from 'ahooks';
import _ from 'lodash';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import { SuperviseListItem } from '@/views/security/components/supervise-list-item/account-list-item';
import SearchDrawer from '@/views/shop-list/components/search-drawer';
import { usePlatformManage, useTagManage } from '@/views/shop-list/hooks';
import SuperviseBtns from '@/views/security/components/supervise-btns';
import { SuperviseSwitch } from '@/views/security/enum';
import SuperPopup from '@/components/super-popup';
import SuperviseSettings from '@/views/security/components/supervise-settings';
import RootStore from '@/stores';
import styles from './styles.module.scss';
import VipBenefits from '@/views/vip-manager/vip-buy/components/vip-benefits';

const LIMIT = 20;
const DEFAULT_PAGE = 1;
const AccountSupervise: React.FC = () => {
  const clientRouter = ClientRouter.getRouter();
  const [filterKeyword, setFilterKeyword] = useState('');
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [showAllOpenFileRecord, setShowAllOpenFileRecord] = useState(false);
  const [tabBarKey, setTabBarKey] = useState<SuperviseSwitch>(SuperviseSwitch.On);
  const [slectSuperviseKeys, setSlectSuperviseKeys] = useState<number[]>([]);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [page, setPage] = useState(DEFAULT_PAGE);
  const userStore = RootStore.instance.userStore;
  const authStore = RootStore.instance.authStore;
  const [visibleBenefits, setVisibleBenefits] = useState(false);

  const tagMge = useTagManage();
  const platformMge = usePlatformManage();
  const loadingComp = useMemoizedFn(() => {
    Toast.show({
      icon: 'loading',
      content: '加载中...',
      duration: 0,
    });
  });
  useEffect(() => {
    loadingComp();
  }, []);
  let { data, loading, runAsync } = useRequest(async (params) => {
    if (!params) {
      params = {
        page: DEFAULT_PAGE,
        search: '',
        is_enable: tabBarKey,
      };
    }
    const [err, res] = await to(
      superviseService.superviseAcctList(
        _.omit({ ...params, limit: LIMIT, is_enable: JSON.parse(params.is_enable) }, ['preData'])
      )
    );
    Toast.clear();
    if (err) return { list: [] };
    const newList = ((params?.preData?.length && params?.preData) || []).concat(res.rows);
    return {
      ...res,
      list: newList,
    };
  });
  const reqParmas = useCreation(() => {
    return {
      search: filterKeyword,
      platform_ids: platformMge?.platform?.map((item) => item?.platform_id),
      tag_ids: tagMge?.tag?.map((item) => item?.id),
      is_enable: tabBarKey,
    };
  }, [filterKeyword, tabBarKey, platformMge.platform, tagMge?.tag]);

  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.count;
    return hasData;
  }, [data?.count, tabBarKey, page]);
  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };

    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };

  const onRefresh = useCallback(async () => {
    RootStore.instance.userStore?.getUsedBenefit();
    setSlectSuperviseKeys([]);
    runAsync({ ...reqParmas, preData: [] });
  }, [reqParmas]);

  const handleResetSearch = useCallback(async () => {
    tagMge.setTag([]);
    platformMge.setPlatform([]);
    setDrawerOpen(false);
    setPage(DEFAULT_PAGE);
    runAsync({
      search: filterKeyword,
      platform_ids: [],
      tag_ids: [],
      is_enable: tabBarKey,
      preData: [],
    });
  }, [tagMge, platformMge.platform]);

  const handleOnOkSearch = () => {
    setPage(DEFAULT_PAGE);
    setDrawerOpen(false);
    runAsync(reqParmas);
  };
  const handleInputChange = (val) => {
    setFilterKeyword(val);
  };
  const handleTabChange = async (key) => {
    loadingComp();
    data.list = [];
    setPage(DEFAULT_PAGE);
    setTabBarKey(key);
    await runAsync({
      ...reqParmas,
      is_enable: key,
      page: DEFAULT_PAGE,
      search: filterKeyword,
      preData: [],
    });
  };
  useDebounceEffect(
    () => {
      setPage(DEFAULT_PAGE);
      runAsync(reqParmas);
    },
    [filterKeyword],
    {
      wait: 300,
    }
  );
  const handlerCheckChange = useCallback(
    (v) => {
      setSlectSuperviseKeys(v as number[]);
    },
    [data?.list, showAllOpenFileRecord]
  );

  const handleFeeTrial = () => {
    setVisibleBenefits(false);
    handleTabChange(SuperviseSwitch.Off);
  }

  useEffect(() => {
    if (userStore.getVipStatus.isVipMember) return;

    if (userStore.getVipStatus.isSuperviseEnabled) {
      setVisibleBenefits(true)
      return;
    }
    if (userStore.getVipStatus.isVipTryoutExpired) {
      Modal.alert({
        title: '',
        content: '「安全管家」服务已过期 平台账号已失去监管',
      });
      return;
    }
    if (userStore.getVipStatus.isVipTryout) {

      Modal.alert({
        title: '',
        content: '免费体验仅可开启1个平台账号的监管 升级安全管家享10个监管名额',
      });
      return;
    }
  }, [
    userStore.getVipStatus.isVipMember,
    userStore.getVipStatus.isSuperviseEnabled,
    userStore.getVipStatus.isVipTryoutExpired,
    userStore.getVipStatus.isVipTryout
  ]);

  return (
    <div className={styles.accountSupervise}>
      <HeaderNavbar
        title={
          <div className={styles.title}>
            <div>事中监管</div>
            <div className={styles.title2}>按账号监管</div>
          </div>
        }
        rightNode={
          <span onClick={() => setSettingsVisible(true)} className={styles.set}>
            设置
          </span>
        }
      />
      <div className={styles.searchWrp}>
        <a href='' />
        <SearchBar
          value={filterKeyword}
          onChange={handleInputChange}
          className={styles.search}
          clearable
          placeholder="请输入平台账号名称"
        />
        <FilterOutline
          className={styles.searchIcon}
          fontSize={20}
          onClick={() => setDrawerOpen(true)}
        />
        <SearchDrawer
          drawerOpen={drawerOpen}
          setDrawerOpen={setDrawerOpen}
          tagMge={tagMge}
          platformMge={platformMge}
          reset={handleResetSearch}
          onOk={handleOnOkSearch}
        />
      </div>
      <SuperviseTabs tabBarKey={tabBarKey} handleTabChange={handleTabChange} />
      <VipTips />
      <div className={styles.content}>
        <Checkbox.Group value={slectSuperviseKeys} onChange={handlerCheckChange}>
          <InfiniteScrollList
            key={tabBarKey}
            data={data?.list}
            renderRow={(data) => (
              <SuperviseListItem
                isEnable={JSON.parse(tabBarKey)}
                key={data.id}
                onFresh={onRefresh}
                data={data as SuperviseModule.SuperviseAccountRow}
              />
            )}
            loading={loading}
            getMore={getMore}
            hasMore={hasMore}
            onRefresh={onRefresh}
            threshold={80}
            emptyText={'暂无数据'}
          />
        </Checkbox.Group>
      </div>
      <footer className={styles.footer}>
        {!!data?.list.length ? (
          <Checkbox
            indeterminate={
              slectSuperviseKeys.length > 0 && slectSuperviseKeys.length < data?.list.length
            }
            checked={slectSuperviseKeys.length === data?.list.length}
            onChange={(checked) => {
              if (checked) {
                const allSelectKeys = data?.list.map((item) => item.id);
                setSlectSuperviseKeys(allSelectKeys);
              } else {
                setSlectSuperviseKeys([]);
              }
            }}
          >
            <span className={styles.allSelect}>全选</span>
          </Checkbox>
        ) : (
          <span />
        )}
        <SuperviseBtns
          slectSuperviseKeys={slectSuperviseKeys}
          onFresh={onRefresh}
          superviseType={tabBarKey}
        />
      </footer>
      <SuperPopup title="设置" visible={settingsVisible} onClose={() => setSettingsVisible(false)}>
        <SuperviseSettings />
      </SuperPopup>

      <VipBenefits
        visible={visibleBenefits}
        onClose={() => {
          setVisibleBenefits(false);
        }}
        showBuy={!userStore.getVipStatus?.isVipMember && authStore.hasVIPBuyAuth}
        onClickFeeTrial={handleFeeTrial}
      />
    </div>
  );
};

export default observer(AccountSupervise);
