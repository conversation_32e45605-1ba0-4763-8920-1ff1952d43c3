import React, { useState, useCallback, useEffect } from 'react';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';
import HeaderNavbar from '@/components/header-navbar';
import ClientRouter from '@/base/client/client-router';
import { useMemoizedFn, useRequest, useCreation, useDebounceEffect } from 'ahooks';
import { Toast } from 'antd-mobile';
import { to } from '@/utils';
import _ from 'lodash';
import { superviseService } from '@/services/supervise';
import { SuperviseType } from '@/views/security/enum';
import InfiniteScrollList from '@/components/infinite-scroll-list';
import LogsItem from './list-item';
import { getTimeRangeByDays } from '@/utils/time';
import { useSearchParams } from 'react-router-dom';

import SearchBar from './search-bar';

const LIMIT = 20;
const DEFAULT_PAGE = 1;
const SuperviseLogs: React.FC = () => {
  const [searchParams] = useSearchParams();
  const keyword = searchParams.get('keyword');
  const initType = searchParams.get('type');
  const clientRouter = ClientRouter.getRouter();
  const [filterKeyword, setFilterKeyword] = useState<string>(decodeURIComponent(keyword || ''));
  const [page, setPage] = useState(DEFAULT_PAGE);
  const [logType, setLogType] = useState<SuperviseType>(
    Number(initType) === SuperviseType.Account ? SuperviseType.Account : SuperviseType.Member
  );
  const [selectDaysVal, setSelectDaysVal] = useState<number>(15);
  const serverTime = useCreation(() => {
    return getTimeRangeByDays(selectDaysVal);
  }, [selectDaysVal]);
  const loadingComp = useMemoizedFn(() => {
    Toast.show({
      icon: 'loading',
      content: '加载中...',
      duration: 0,
    });
  });
  useEffect(() => {
    loadingComp();
  }, []);

  const reqParmas = useCreation(() => {
    let serverParams = {
      start_time: serverTime.startTimestamp,
      end_time: serverTime.endTimestamp,
      ...(logType === SuperviseType.Account
        ? { account_name: filterKeyword }
        : { staff_name: filterKeyword }),
    };

    return serverParams;
  }, [filterKeyword, serverTime]);

  let { data, loading, runAsync } = useRequest(async (params) => {
    if (!params) {
      params = {
        page: DEFAULT_PAGE,
        ...reqParmas,
      };
    }
    const serverParams = {
      ...params,
      limit: LIMIT,
    };

    const [err, res] = await to(
      superviseService.logList(_.omit(serverParams, ['preData']) as SuperviseModule.LogListParams)
    );
    Toast.clear();
    if (err) return { list: [] };
    const newList = ((params?.preData?.length && params?.preData) || []).concat(res.list);
    return {
      ...res,
      list: newList,
    };
  });

  const hasMore = useCreation(() => {
    const hasData = page * LIMIT < data?.count;
    return hasData;
  }, [data?.count, page]);
  const getMore = async () => {
    if (!hasMore) return;
    const newPage = page + 1;
    const params = {
      ...reqParmas,
      page: newPage,
    };

    setPage(newPage);
    await runAsync({ ...params, preData: data?.list });
  };

  const onRefresh = useCallback(
    async (mergeParams = {}) => {
      runAsync({ ...reqParmas, preData: [], ...mergeParams });
    },
    [reqParmas]
  );

  useDebounceEffect(
    () => {
      setPage(DEFAULT_PAGE);
      runAsync(reqParmas);
    },
    [filterKeyword, logType, selectDaysVal],
    {
      wait: 300,
    }
  );
  return (
    <div className={styles.superviseLogs}>
      <HeaderNavbar onBack={() => clientRouter.goBack()} title="事中监管日志" />
      <SearchBar
        filterKeyword={filterKeyword}
        logType={logType}
        setFilterKeyword={setFilterKeyword}
        setLogType={setLogType}
        selectDaysVal={selectDaysVal}
        setSelectDaysVal={setSelectDaysVal}
      />
      <div className={styles.content}>
        <InfiniteScrollList
          data={data?.list}
          renderRow={(data: SuperviseModule.LogsItem) => (
            <LogsItem key={data.account_id} data={data} />
          )}
          loading={loading}
          getMore={getMore}
          hasMore={hasMore}
          onRefresh={onRefresh}
          threshold={80}
          emptyText={'暂无数据'}
        />
      </div>
    </div>
  );
};

export default observer(SuperviseLogs);
