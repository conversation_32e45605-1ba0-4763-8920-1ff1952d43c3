export const Modules = {
  AuthoModule: {
    key: 'AuthoModule',
    action: {
      ShowSwitchLoading: 'ShowSwitchLoading',
      CloseSwitchLoading: 'CloseSwitchLoading',
      GotoLogin: 'GotoLogin',
      GetMachineInfo: 'GetMachineInfo',
      GetLoginInfo: 'GetLoginInfo',
      Login: 'Login',
      OnLogin: 'OnLogin',
      OnLogout: 'OnLogout',
      Logout: 'Logout',
      UCloudLogin: 'UCloudLogin',
    },
  },
  WsAuthoModule: {
    key: 'WsAuthoModule',
    action: {
      ClientConnected: 'ClientConnected',
      HeartBeat: 'HeartBeat',
      // 客户端接收到这个消息以后才可以请求前端，否则会被全都抛弃
      OnPluginInitialized: 'OnPluginInitialized',
    },
  },

  // 由客户端代替发起请求
  ServerAPIModule: {
    key: 'ServerAPIModule',
    action: {
      // 发起Http请求
      RequestHttp: 'RequestHttp',
    },
  },
  StorageModule: {
    key: 'StorageModule',
    action: {
      // 获取本地用户数据
      GetLocalStorage: 'GetLocalStorage',
      // 保存本地用户数据
      SetLocalStorage: 'SetLocalStorage',
      GetAppInstallData: 'GetAppInstallData',
      SetAppInstallData: 'SetAppInstallData',
      // 从运行内存中取
      SetSessionStorage: 'SetSessionStorage',
      GetSessionStorage: 'GetSessionStorage',
    },
  },
  WebBrowserManagerModule: {
    key: 'WebBrowserManagerModule',
    action: {
      ClearBrowserUserData: 'ClearBrowserUserData',
      OpenWebBrowser: 'OpenWebBrowser',
      OpenBrowserManagement: 'OpenBrowserManagement',
      OnOpenSucceedBrowser: 'OnOpenSucceedBrowser',
      OnOpenFailureBrowser: 'OnOpenFailureBrowser',
      OnRunningBrowser: 'OnRunningBrowser',
      OnCloseBrowser: 'OnCloseBrowser',
      ClearCache: 'ClearCache',
      GetStoreList: 'GetStoreList',
      GetOpenStoreStatusList: 'GetOpenStoreStatusList',
      ReplaceBrowserTag: 'ReplaceBrowserTag',
      // 中间代理模式启动店铺
      OpenWebBrowserIntermediaryModel: 'OpenWebBrowserIntermediaryModel',
    },
  },
  LogModule: {
    key: 'LogModule',
    action: {
      CopyLogFilePath: 'CopyLogFilePath',
      OnNativePrintLog: 'OnNativePrintLog',
    },
  },
  SystemInfoModule: {
    key: 'SystemInfoModule',
    action: {
      ClipText: 'ClipText',
      GetLanguageSetting: 'GetLanguageSetting',
      SetLanguageSetting: 'SetLanguageSetting',
      GetVersion: 'GetVersion',
      GetReleasePhase: 'GetReleasePhase',
      CheckNewVersion: 'CheckNewVersion',
      SetRunTimeDebugControl: 'SetRunTimeDebugControl',
      GetClientConfig: 'GetClientConfig',
      OnLanguageChange: 'OnLanguageChange',

      // 获取是否启用硬件加速
      GetHardwareAcceleration: 'GetHardwareAcceleration',

      // 设置是否启用硬件加速
      SetHardwareAcceleration: 'SetHardwareAcceleration',

      // 获取运行模式
      GetBrowserRunModel: 'GetBrowserRunModel',
      // 设置浏览器运行模式
      SetBrowserRunModel: 'SetBrowserRunModel',
      // SEMS API Url前缀变更事件
      OnSEMSApiPreUrlChange: 'OnSEMSApiPreUrlChange',

      // 获取API中转线路信息
      GetNetLineInfo: 'GetNetLineInfo',
      // 选择API中转线路
      SetSelectNetLineKey: 'SetSelectNetLineKey',
      // 获取摄像头选项
      GetWebCamIndex: 'GetWebCamIndex',
      // 设置摄像头选项
      SetWebCamIndex: 'SetWebCamIndex',
      // 获取SEMS Api Url前缀 (因为SSOS的api已经是都走客户端了)
      GetSEMSApiPreUrl: 'GetSEMSApiPreUrl',
      // Api线路信息变更
      OnNetLineInfoChange: 'OnNetLineInfoChange',

      // 获取 Mac地址
      GetMacs: 'GetMacs',
      // AddCert
      AddCert: 'AddCert',

      // 设置开发环境的前缀
      SetServerUrl: 'SetServerUrl',

      // 获取内核版本号
      GetCoreVersion: 'GetCoreVersion',

      // 获取所有可二次配置的地址
      GetApiPreUrlInfo: 'GetApiPreUrlInfo',

      // 设置某一个api前缀
      SetCustomApiPreUrl: 'SetCustomApiPreUrl',

      // 当前是否有新版本可用
      HaveNewVersion: 'HaveNewVersion',

      // 获取当前网页模式
      GetPageDisplayType: 'GetPageDisplayType',

      // 设置当前网页模式
      SetPageDisplayType: 'SetPageDisplayType',

      // 通知客户端长按
      LongTouch: 'LongTouch',

      // 获取当前可用推送选项
      GetPushMessageList: 'GetPushMessageList',

      // 设置当前推送选项
      SetPushMessageConfig: 'SetPushMessageConfig',

      // 获取位数
      GetCurrentVersion: 'GetCurrentVersion',
    },
  },

  DataObserverModule: {
    key: 'DataObserverModule',
    action: {
      OnStoreInfoChange: 'OnStoreInfoChange',
      OnHasNewMessage: 'OnHasNewMessage',
      NotifyMsgReadStateChange: 'NotifyMsgReadStateChange',
      OnMsgReadStateChange: 'OnMsgReadStateChange',
      NotifyStoreInfoChange: 'NotifyStoreInfoChange',
      OnCommonStateChange: 'OnCommonStateChange',
      NotifyCommonStateChange: 'NotifyCommonStateChange',
    },
  },

  PayModule: {
    key: 'PayModule',
    action: {
      AliPay: 'AliPay',
      WeChatPay: 'WeChatPay',
    },
  },
  AnalysisModule: {
    key: 'SensorsAnalysisModule',
    action: {
      GetProperties: 'GetProperties',
      RecordCountEvent: 'RecordCountEvent',
    },
  },
  MenuModule: {
    key: 'MainActivity',
    action: {
      OnTabBarIndexChange: 'OnTabBarIndexChange',
    },
  },
};
