import { observer } from 'mobx-react';
import { FC } from 'react';
import { VIPBuyType } from '../../../const';
import Store from '../../../store';
import styles from './style.module.scss';
import SystemStore from '@/stores/system';
import { useInjectedStore } from '@/hooks/useStores';
import { Space } from 'antd-mobile';
import vipPng from '../../../images/vip.png';
 
interface IProps {
  store: Store;
}

const Package: FC<IProps> = ({ store }) => {
  const system: SystemStore = useInjectedStore('systemStore');
  const globalCurrency = system.globalCurrency;
  const { selectedPackage, allPackageData, setSelectedPackageId } = store;
  if (store.VIPBuyType === VIPBuyType.Scaling) return null;

  return (
    <div className={styles.package}>
      <Space style={{ '--gap': '12px' }}>
        {allPackageData.map((item) => (
          <div
            onClick={() => {
              setSelectedPackageId(item.id);
            }}
            className={`${styles.card} ${selectedPackage?.id !== item.id && styles.noSelected}`}
            key={item?.id}
          >
            {item?.discount < 100 ? (
              <span className={styles.discountTag}>
                {item?.discount / 10}折
              </span>
            ) : (
              ''
            )}
            <div className={styles.days}>
              <span>
                <span style={{ marginLeft: 4 }}>
                  {item?.period}
                  天套餐
                </span>
              </span>
            </div>

            <div className={styles.priceWrp}>
              <span className={styles.priceText}>
                {globalCurrency} <span className={styles.price}>{item?.discount_price}</span>
              </span>
            </div>

            <div className={styles.discountWrp}>
              <span className={styles.discount}>
                {globalCurrency}
                {item?.price?.toFixed(2)}
                {/* /{item?.period}
                天 */}
              </span>
            </div>

            {selectedPackage?.id === item.id && (<img src={vipPng} className={styles.vipImg} />)}
          </div>
        ))}
      </Space>
    </div>
  );
};

export default observer(Package);
