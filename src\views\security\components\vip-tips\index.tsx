import React from 'react';
import { observer } from 'mobx-react';
import RootStore from '@/stores';
import { useCreation } from 'ahooks';
import styles from './styles.module.scss';
import { RightOutline } from 'antd-mobile-icons';
import useVipText from '@/hooks/useVipText';

const VipTipItem: React.FC<{ content: string; action: React.ReactNode }> = observer((params) => {
  const { content, action } = params;
  return (
    <div className={styles.vipItem}>
      <span>{content}</span>
      <span>
        {action}
        <RightOutline color="var(--adm-color-primary)" />
      </span>
    </div>
  );
});
const VipTips: React.FC = () => {
  const { isSuperviseEnabled, isVipPurchased, isExpireding, isExpired, isVipMember } =
    RootStore?.instance?.userStore?.getVipStatus;
  const vipText = useVipText() || '';
  const vipTip = useCreation(() => {
    if (!isSuperviseEnabled && !isVipPurchased) {
      return <VipTipItem content={vipText} action={<a>升级</a>} />;
    }
    if (isExpireding) {
      return <VipTipItem content={vipText} action={<a>续费</a>} />;
    }
    if (isExpired) {
      return <VipTipItem content={vipText} action={<a>升级</a>} />;
    }
    if (isVipMember) {
      return <VipTipItem content={vipText} action={<a>加购</a>} />;
    }
    return null;
  }, [vipText]);

  return <div className={styles.vipTips}>{vipTip}</div>;
};

export default observer(VipTips);
