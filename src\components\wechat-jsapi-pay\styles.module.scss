.wechatPayButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  
  .wechatIcon {
    font-size: 20px;
    color: #07c160;
  }
  
  &:disabled {
    .wechatIcon {
      color: #c8c9cc;
    }
  }
}

.paymentModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  text-align: center;
  
  .paymentIcon {
    font-size: 48px;
    color: #07c160;
    margin-bottom: 16px;
    
    svg {
      display: block;
    }
  }
  
  .paymentText {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }
  
  .paymentDesc {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .wechatPayButton {
    font-size: 14px;
    
    .wechatIcon {
      font-size: 18px;
    }
  }
  
  .paymentModal {
    padding: 20px;
    
    .paymentIcon {
      font-size: 40px;
      margin-bottom: 12px;
    }
    
    .paymentText {
      font-size: 16px;
    }
    
    .paymentDesc {
      font-size: 13px;
    }
  }
}
