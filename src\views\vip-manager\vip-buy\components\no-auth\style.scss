.box {
  height: 100%;
  .content {
    position: relative;
    bottom: 48px;
    display: flex;
    align-items: center;
    // justify-content: center;
    flex-direction: column;
    height: 100%;
    margin-top: 200px;
    .pic {
      width: 450px;
      height: 240px;
      // background: url('./images/bg.png') no-repeat;
    }

    .text {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .title {
      font-size: 24px;
      font-family: PingFang SC, PingFang SC;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.88);
      margin-top: 36px;
    }

    .tips {
      font-family: PingFang SC, PingFang SC;
      color: rgba(0, 0, 0, 0.65);

      a {
        margin-left: 5px;
        color: #3569FD;
      }

      &::before {
        position: absolute;
        content: '';
        left: 0;
        top: 50%;
        width: 5px;
        height: 5px;
        background-color: #3569FD;
        border-radius: 50%;
        transform: translateY(-50%);
      }
    }

    .btns {
      display: flex;
      flex-direction: column;
    }
  }
}
.check{
  color: #3569FD;
  cursor: pointer;
}