import { SuperNotification } from '@/components';
import { httpService } from '@/apis';
import { PaymentTypes, SuperviseType, VIPBuyType } from '@/views/vip-manager/vip-buy/const';
import { to } from '@ziniao-fe/core';
import { errorHandler } from '@/utils/errors';

export const payApi = {
  wechatpay(data: any) {
    return httpService<any>({
      url: '/order/wechatpay',
      method: 'POST',
      data,
    });
  },

  alipay(data: any) {
    return httpService<any>({
      url: '/order/ali_qrcode_pay',
      method: 'POST',
      data,
    });
  },

  ploypay(data: any) {
    return httpService<any>(
      {
        url: '/order/ploypay',
        method: 'POST',
        data,
      },
      {
        alertError: false,
      }
    );
  },
  getOrderStatus(data: any) {
    return httpService<any>({
      url: '/order/status',
      method: 'POST',
      data,
    });
  },
  cancelOrder(data: any) {
    return httpService<any>({
      url: '/order/close',
      method: 'POST',
      data,
    });
  },
}

const VipService = {
  /**加购套餐 */
  expandPackage() {
    return httpService<any>({
      url: '/member/center/supervise/package',
      method: 'POST',
    })
  },
  /**会员套餐 */
  packageData() {
    return httpService<any>({
      url: '/member/center/package',
      method: 'POST',
    })
  },
  /**添加会员订单 */
  createOrder(data: {
    /** 会员套餐数据	 */
    member_package: {
      package_id: number,
      count: number
    },
    /** 加购套餐数据 */
    supervise_package: {
      package_id: number,
      count: number
    }[],
    /** 金额信息 */
    pay_price: {
      /** 总金额	 */
      total: number,
      /** 折扣金额	 */
      discount: number,
      /** 实付金额	 */
      paid: number,
    },
    /** 支付方式 1 (支付宝 聚合) 2 微信  3余额	 */
    pay_method: number,
    /** 实付金额	 */
    auto_renew: boolean,
    /** 类型： 1新购 2续费 3 加购	 */
    type: VIPBuyType
  }) {
    return httpService<any>({
      url: '/member/center/order',
      method: 'POST',
      data
    })
  },
  /** 会员订单聚合支付状态 */
  getAggregateStatus() {
    return httpService<any>({
      url: '/member/center/order/ploypay/status',
      method: 'POST',
    })
  },


  /** 获取续费订单信息 */
  getRenewOrderInfo() {
    return httpService<any>({
      url: '/member/center/renew/order/info',
      method: 'POST',
    })
  },
  /** 会员中心 已使用情况 */
  getUsedBenefit() {
    return httpService<any>({
      url: '/member/center/used/benefit',
      method: 'POST',
    })
  },
  /** 会员中心 会员已购买权益信息-事中监管记录存储 */
  getReviewDetail() {
    return httpService<VipAPI.ReviewDetail>({
      url: '/member/center/supervise/day/detail',
      method: 'POST',
    })
  },
  /** 会员中心 会员已购买权益信息-事中监管成员 */
  getMemberDetail() {
    return httpService<VipAPI.MemberDetail>({
      url: '/member/center/supervise/user/detail',
      method: 'POST',
    })
  },
  /** 会员中心 会员已购买权益信息-事中监管平台账号 */
  getAccountDetail() {
    return httpService<VipAPI.AccountDetail>({
      url: '/member/center/supervise/account/detail',
      method: 'POST',
    })
  },
  /** 会员中心 会员自动续费标记修改 */
  changeAutoRenewStatus(data: any) {
    return httpService({
      url: '/member/center/auto/renew/status',
      method: 'POST',
      data
    })
  },
  /**检查非会员查看次数 */
  checkOriginalPersonReview(data: any) {
    return httpService<VipAPI.ReviewInfo>({
      url: '/supervise/check_view_limit',
      method: 'POST',
      data,
    }, {
      alertError: false,
    })
  },
  /** 会员事中监管记录查看时长调整活动信息 */
  memberCenterEventData() {
    return httpService<any>({
      url: '/member/center/event_data',
      method: 'POST',
    }, {
      alertError: false,
    })
  },
  /** 购买会员加购预估数量 */
  memberPredictAddNum(data: { predict_type: VIPBuyType }) {
    return httpService<any>({
      url: '/member/center/predict_add_num',
      method: 'POST',
      data
    })
  },
  /** 切换监管模式 */
  switchSuperviseType: (data: {
    supervise_type: SuperviseType;
  }) => {
    return httpService<any>(
      {
        url: '/member/center/switch_supervise_type',
        method: 'POST',
        data
      },
    );
  }
};

export default VipService;
