.expanPackage {
  font-family: PingFang SC, PingFang SC;
  margin-top: 16px;

  .titleWrp {
    .title {
      font-family: PingFang SC, PingFang SC;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.45);
      font-weight: bold;
    }

  }

  .table {
    width: calc(100vw - 32px);
    margin-top: 4px;
    .item {
      border-radius: 4px;
      padding: 12px;
      background-color: white;
      display: flex;
      justify-content: space-between;

      .rigthWrp {
        .name {
          font-size: 16px;
          color: rgba(0, 0, 0, 0.88);
          font-weight: bold;
        }

        .priceWrp {
          display: flex;
          align-items: center;
          margin-top: 8px;
          .discountPriceWrp {
            display: flex;
            align-items: baseline;

            .rmb {
              color: #FF4D4F;
              font-size: 10px;
            }

            .discountPrice {
              color: #FF4D4F;
              font-size: 20px;
              font-weight: bold;
              margin-left: 2px;
              margin-right: 4px;
            }
          }


          .price {
            color: $color-text-tertiary;
            text-decoration-line: line-through;
          }

          .unit {
            color: $color-text-tertiary;
          }
        }
      }

      .stepperWrp {
        display: flex;
        align-self: end;
      }
    }
  }



  .addSubText {
    color: $color-text-tertiary;
  }

  .typeName {
    color: $color-text;
  }
}

// .priceHeader {
//   min-width: 80px;
// }

.unitPriceHeader {
  min-width: 130px;
}

.typeHeader {
  min-width: 180px;
}
