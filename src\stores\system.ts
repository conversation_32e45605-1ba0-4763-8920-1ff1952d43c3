import { computed, makeAutoObservable, observable, runInAction } from 'mobx';
import { clientSdk } from '@/apis';
import { ConstValues, Logs, to } from '@/utils';
import publicService from '@/services/public';
import { getLocale } from '@/i18n';
import SuperToast from '@/components/super-toast';
import deviceService from '@/services/device';
import AndroidSdk from '@/base/client/android/index';
import configs from "@/configs";
import { isMiniProgram, isH5Program  } from '@/utils/platform';
import iOSSdk from '@/base/client/ios';
import { tools } from '@/utils/tools';
import { LocalSettingsKeyMaps, SettingKeys } from '@/base/client/enum';
export const DEFAULT_CONFIG =
  '{"SSOS":"https://sbstoretestapi.ziniao.com/test/api/v5/","COMM":"https://sbcommtestapi.ziniao.com/test/api/v5/","SEMS":"https://sbenttestapi.ziniao.com/test/api/v5/","SSMS":"https://sbsiptestapi.ziniao.com/test/api/v3/","SBBL":"https://sbburylogtestapi.ziniao.com/","RPAD":"https://test-rpadesign.ziniao.com/rpa-editor/","RPAL":"https://test-rpacloud.ziniao.com/local-rpa-web/","SBAPP":"https://test-sbappstoreapi.ziniao.com/rest/","CLOUDRPA":"https://test-rpacloud.ziniao.com/cloud-rpa-web/","SGROWTH":"https://test-work-flow-service.ziniao.com/","SGROWTH_ADMIN":"https://dev-common-service-todo-front.ziniao.com/","PUSH":"wss://test-sbwss-zx.ziniao.com/wss/test/","RPAS":"https://dev-rpascheduler-v1.ziniao.com/dev/api/v3/","SERVICE_RPALOG":"https://test-rpalog.ziniao.com/","ADMIN_URL":"index", "CLOUDAPPURL":"https://test-web.ziniao.com","OFFICIAL_API_URL":"https://sbofficewebtestapi.ziniao.com/test/api/v3"}';
export const SettingsStorageKey = 'GetApiPreUrlInfo';

export default class SystemStore {
  version = '*******';
  /** 机器码信息 */
  machineData?: SuperClient.MachineInfoData;
  /** 客户端运行时环境配置 */
  clientConfig?: SuperClient.Config;
  /** 当前客户端语言 */
  language: SuperClient.Language = getLocale();
  /** 公共属性 */
  publicProperties = null;
  serverTimeInited = false;
  /** 服务器当前时间 */
  serverTime = Date.now();
  pluginVersion: string = '';
  bitVersion: string = '';
  hasNewVersion = true;
  checkVersionLoading = false;
  releaseFhase: number = 0;
  systemSettings;
  globalCurrency = '¥';
  subscriberAdToRemote!: {
    open_guide: string;
    open_guide_url: string;
    pc_discount: string;
    pc_no_discount: string;
  };
  /** mac地址 */
  @computed get macAddressInfo() {
    return this.machineData?.macAddresses ?? [];
  }

  constructor() {
    makeAutoObservable(this);

    this.getServerTime();
  }

  /** 客户端准备就绪, 可以开始进行请求以及渲染UI */
  @computed get clientReady() {
    return !!this.machineData && !!this.clientConfig && !!this.version;
  }

  /** 客户端连接后获取服务端请求所需必要信息 */
  clientInitilize = () => {
    const client = isMiniProgram() ? 'mini-program' : isH5Program() ? 'h5-program' : __IOS_CLIENT__ ? 'iOS' : 'Android';
    console.log(
      `%c✅ [${client}] Client connected successfully! 🚀 spent: ${
        Date.now() - window.__START__TIME__
      } ms`,
      'color: #52c41a;font-weight: 700'
    );  
    const isAndroid = __Android_CLIENT__ && !isH5Program() && !isMiniProgram();
    try {
      this.getMachineData();
      this.getPublicProperties();
      this.getClientConfig();
      this.getVersionInfo();
      if (isAndroid) {
        this.getHasNewVersion();
        this.getPluginVersion();
        this.getBitVersion();
        this.getReleasePhase();
      }
      isAndroid && this.andRoidConfig();
      __IOS_CLIENT__ && this.getCachedSettings();
    } catch (error) {
      Logs.error('[mobile] clientInitilize error');
    }
  };

  getPublicProperties = async () => {
    const [err, response] = await to(clientSdk.getPublicProperties());
    if (err) return;
    Logs.warn('[mobile]getPublicProperties', response);
    this.setPublicProperties(response!);
  };
  //根据客户端环境判断是否是开发环境
  get isDevelepment(): boolean {
    return ['dev', 'test', 'sim'].indexOf(this.clientConfig?.env || 'dev') >= 0;
  }
  getMachineData = async () => {
    const [err, response] = await to(clientSdk.getMachineInfo());
    if (err) return;
    Logs.warn('[mobile]getMachineData', response);
    this.setMachineData(response!);
  };

  getVersionInfo = async () => {
    const [err, response] = await to(clientSdk.getVersionInfo());
    if (err) return;
    Logs.warn('[mobile]getVersionInfo', response);
    this.setClientVersion(response as string);
  };
  getClientConfig = async () => {
    const [err, response] = await to(clientSdk.getClientConfig());
    if (err) return;
    Logs.warn('[mobile]getClientConfig', response);
    this.setClientConfig(response!);
  };

  getServerTime = async () => {
    try {
      const res = await publicService.getServerTime();
      if (res?.status === 200) {
        runInAction(() => {
          this.serverTime = res?.data?.timestamp || Date.now();
        });
      }
    } catch (err) {
      runInAction(() => {
        this.serverTime = Date.now();
      });
    } finally {
      runInAction(() => {
        if (!this.serverTimeInited) this.serverTimeInited = !!this.serverTime;
      });
    }
  };

  setMachineData = (machineData: SuperClient.MachineInfoData) => {
    runInAction(() => {
      this.machineData = machineData;
    });
  };

  setPublicProperties = (properties: any) => {
    runInAction(() => {
      const $pp = (this.publicProperties = properties);
      // superSensorsTool.setPublicProperties($pp);
    });
  };

  setClientVersion = (version: string) => {
    runInAction(() => {
      this.version = version;
    });
  };

  setClientConfig = (conf: SuperClient.Config) => {
    runInAction(() => {
      this.clientConfig = conf;
    });
  };
  getHasNewVersion = async () => {
    const [err, response] = await to((clientSdk.clientSdkAdapter as AndroidSdk).haveNewVersion());
    if (err) return (this.hasNewVersion = false);
    Logs.warn('[mobile]haveNewVersion', response);
    this.hasNewVersion = true;
  };
  /**
   * 版本检测
   */
  onCheckVersion = async () => {
    if (this.checkVersionLoading) {
      return;
    }
    if (__IOS_CLIENT__) {
      this.checkVersionLoading = true;
      const res = await (clientSdk.clientSdkAdapter as iOSSdk).checkUpdate();
      if(!res){
        console.log('[mobile]checkUpdate res is empty!!');
      }
      runInAction(() => {
        this.checkVersionLoading = false;
      });
      return;
    }
    let newString;
    this.checkVersionLoading = true;
    const [err, response] = await to((clientSdk.clientSdkAdapter as AndroidSdk).checkNewVersion());
    try {
      if (response == 0) {
        newString = '有新版本可用';
      } else if (response == 1) {
        newString = '当前版本已经是最新版本';
        SuperToast.success(newString);
      } else if (response == 2) {
        newString = ConstValues.httpErrorMessages.NETERROR;
        SuperToast.error(newString);
      } else {
        newString = '检查失败';
        SuperToast.error(newString);
      }
    } catch (error) {
      newString = '检测异常';
      SuperToast.error(newString);
    } finally {
      runInAction(() => {
        this.checkVersionLoading = false;
      });
    }
    return newString;
  };
  get versionRelease() {
    if (__IOS_CLIENT__) return this.version;
    return `${this.version} ${
      this.releaseFhase === ConstValues.releaseStatus.RELEASE ? '' : 'Beta'
    }`;
  }
  /**
   * 获取位数
   */
  getBitVersion = async () => {
    const [err, response] = await to(
      (clientSdk.clientSdkAdapter as AndroidSdk).GetCurrentVersion()
    );
    if (err) return;
    Logs.warn('[mobile]GetCurrentVersion', response);
    this.bitVersion = response as string;
  };

  /**
   * 获取插件版本号
   */
  getPluginVersion = async () => {
    const [err, response] = await to((clientSdk.clientSdkAdapter as AndroidSdk).getCoreVersion());
    if (err) return;
    Logs.warn('[mobile]getPluginVersion', response);
    this.pluginVersion = response as string;
  };
  /**
   * 获取当前版本是测试版本或者是正式版本, 测试版本会在版本号旁边加beta
   */
  getReleasePhase = async () => {
    const [err, response] = await to((clientSdk.clientSdkAdapter as AndroidSdk).getReleasePhase());
    this.releaseFhase = response as number;
    Logs.warn('[mobile]getReleasePhase', response);
    return response;
  };
  get pluginVersionRelease() {
    if (this.pluginVersion) {
      return `V${this.pluginVersion}`;
    } else {
      return '暂无插件信息';
    }
  }
  getStaticConfig = async () => {
    const env = this.clientConfig?.env || 'production';
    const officialApiUrl = configs?.[env]?.officialApiUrl;
    const [err, res] = await to(deviceService.getStaticConfig(officialApiUrl));
    if (err) return;
    Logs.warn('[mobile]getStaticConfig', res);
    runInAction(() => {
      this.subscriberAdToRemote = res?.subscriber_ad_to_remote;
    });
  };
  /** 获取环境配置 */
  getCachedSettings = async () => {
    try {
      const [err, res] = await to(
        (clientSdk.clientSdkAdapter as iOSSdk).getCachedConfigByKey(SettingsStorageKey)
      );
      let data = tools.safeParseJSON(res || DEFAULT_CONFIG);
      Logs.log('getCachedConfigByKey-res', res);
      // 设置初始数据
      if (!res) {
        const [err, res] = await to(
          (clientSdk.clientSdkAdapter as iOSSdk).setCachedConfigByKey(
            SettingsStorageKey,
            JSON.stringify(data)
          )
        );
        if (err) {
          Logs.error('getCachedSettings--->setCachedConfigByKey:error:', err);
          return;
        }
        Logs.log('getCachedSettings--->setCachedConfigByKey:success:', res);
      }
      this.systemSettings = data;
    } catch (e) {
      this.systemSettings = JSON.parse(DEFAULT_CONFIG);
      Logs.error(e);
    }
  };
  /** 设置环境配置 */
  setSettings = async (key: SettingKeys, value: string) => {
    const data = { ...(this.systemSettings || {}) };
    data[key] = value;
    this.systemSettings = data;
    const [err, res] = await to(
      (clientSdk.clientSdkAdapter as iOSSdk).setCachedConfigByKey(
        SettingsStorageKey,
        JSON.stringify(data)
      )
    );
    const StorageKey = LocalSettingsKeyMaps.get(key);
    if (!!StorageKey) {
      console.log('StorageKey', StorageKey, value);
      (clientSdk.clientSdkAdapter as iOSSdk).setCachedConfigByKey(StorageKey, value);
    }
    if (err) {
      Logs.error('setSettings--->setCachedConfigByKey:error:', err);
      return;
    }
    Logs.log('setSettings--->setCachedConfigByKey:success:', res);
    SuperToast.success('设置成功');
  };
  andRoidConfig = async () => {
    const [err, res] = await to((clientSdk.clientSdkAdapter as AndroidSdk).getApiPreUrlInfo());
    if (err) return;
    this.systemSettings = JSON.parse(res);
  };
}
