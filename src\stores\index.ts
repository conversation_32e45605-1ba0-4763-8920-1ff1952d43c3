import _ from 'lodash';
import {
  observable,
  action,
  when,
  makeObservable,
  reaction,
  autorun,
  makeAutoObservable,
} from 'mobx';
import SDK from '@/base/client';
import BrowserStore from '@/components/super-start-browser/stores/browser';
import SystemStore from './system';
import UserStore from './user';
import AuthStore from './auth';
import ExtraUserStore from './extra-user';
import { request } from '@/apis';
import { Logs, urlTool } from '@/utils';
import { CACHED_LOGIN_INFO_KEY } from '@/constants';
import { isH5Program, isMiniProgram } from '@/utils/platform';
import BuriedPoint from '@/models/buried-point';

export default class RootStore {
  clientInited = false;
  static instance: RootStore;
  userStore!: UserStore;
  authStore!: AuthStore;
  extraUserStore!: ExtraUserStore;
  systemStore!: SystemStore;
  browserStore!: BrowserStore;
  constructor() {
    makeAutoObservable(this);
    const { code, token } = urlTool.getUrlCodeOrToken();
    /**如果是code或者token登录，清除缓存的登录信息 */
    if (code || token) {
      this.clearH5OrMiniprogramLoginInfoCache();
    }
    if (RootStore?.instance) return RootStore?.instance;
    this.systemStore = new SystemStore();
    this.extraUserStore = new ExtraUserStore();
    this.authStore = new AuthStore();
    this.userStore = new UserStore();
    this.browserStore = new BrowserStore();

    RootStore.instance = this;
    this.init();

    return this;
  }
  clearH5OrMiniprogramLoginInfoCache() {
    localStorage.removeItem(CACHED_LOGIN_INFO_KEY);
  }
  setClientInited(inited: boolean) {
    this.clientInited = inited;
  }
  init = async () => {
    // 客户端连接上，去获取机器码等基础信息
    await when(() => !!SDK.ready, this.systemStore.clientInitilize);
    // 机器码、密钥等必要信息获取完成初始化请求服务
    try {
      await when(() => !!this.systemStore.clientReady, request.initilize);
    } catch (error) {
      Logs.error('initilize 初始化失败，多一次尝试', error);
      await when(() => !!this.systemStore.clientReady, request.initilize);
    }
    reaction(
      () => this.userStore?.loginInfo,
      (info) => {
        if (!!info) {
          request.loadUser({
            userId: info?.id!,
            companyId: info?.company!,
            userSecretKey: info?.oauth_string!,
          });
          //初始化的一些请求
          this.authStore.setUserRoleInfo(
            _.pick(this.userStore?.loginInfo!, [
              'identity_id',
              'is_company_whitelist',
              'permissions',
              'permissions_groups',
              'role_id',
              'role_name',
            ]) as UserService.PermissionInfo
          );
          /** 切换公司页面webview不请求数据 */
          const user_id_switch = urlTool.getQueryString('user_id_switch');
          // 登录页面不请求数据
          const isloginPage = location.hash.includes('login');
          if (user_id_switch || isloginPage) return;
          this.userStore?.fetchExtraUserInfo();
          this.userStore?.getUserInfoNew();
          this.userStore?.getUserData();
          this.systemStore?.getStaticConfig();
          if (isH5Program() || isMiniProgram()) {

          }
        } else {
          // 退出登录重置一些状态、内存等
          request.loadUser(undefined);
        }
      }
    );
  };
}
export { RootStore };
