/**
 * 微信支付工具函数
 */

/**
 * 检测微信浏览器环境
 */
export const detectWechatEnvironment = () => {
  const ua = navigator.userAgent.toLowerCase();
  const isWechat = ua.includes('micromessenger');
  const isWechatWork = ua.includes('wxwork');
  const isWechatMiniProgram = window.__wxjs_environment === 'miniprogram';
  
  return {
    isWechat,
    isWechatWork,
    isWechatMiniProgram,
    isWechatBrowser: isWechat && !isWechatMiniProgram,
    userAgent: ua,
  };
};

/**
 * 格式化金额（元转分）
 * @param amount 金额（元）
 * @returns 金额（分）
 */
export const formatAmountToFen = (amount: number | string): number => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numAmount)) {
    throw new Error('无效的金额格式');
  }
  return Math.round(numAmount * 100);
};

/**
 * 格式化金额（分转元）
 * @param amount 金额（分）
 * @returns 金额（元）
 */
export const formatAmountToYuan = (amount: number | string): number => {
  const numAmount = typeof amount === 'string' ? parseInt(amount) : amount;
  if (isNaN(numAmount)) {
    throw new Error('无效的金额格式');
  }
  return numAmount / 100;
};

/**
 * 生成订单号
 * @param prefix 前缀
 * @returns 订单号
 */
export const generateOrderNo = (prefix: string = 'WX'): string => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}${timestamp}${random}`;
};

/**
 * 验证订单号格式
 * @param orderNo 订单号
 * @returns 是否有效
 */
export const validateOrderNo = (orderNo: string): boolean => {
  // 订单号应该是字母数字组合，长度在6-32位之间
  const regex = /^[a-zA-Z0-9]{6,32}$/;
  return regex.test(orderNo);
};

/**
 * 格式化支付描述
 * @param description 原始描述
 * @param maxLength 最大长度
 * @returns 格式化后的描述
 */
export const formatPayDescription = (description: string, maxLength: number = 127): string => {
  if (!description) {
    return '商品支付';
  }
  
  // 移除特殊字符，只保留中文、英文、数字和常用符号
  const cleaned = description.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）]/g, '');
  
  // 截断到指定长度
  return cleaned.length > maxLength ? cleaned.substring(0, maxLength) : cleaned;
};

/**
 * 解析微信支付回调URL参数
 * @param url 回调URL
 * @returns 解析后的参数对象
 */
export const parseWechatCallbackParams = (url?: string): Record<string, string> => {
  const targetUrl = url || window.location.href;
  const urlObj = new URL(targetUrl);
  const params: Record<string, string> = {};

  // 解析查询参数
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value;
  });

  // 解析hash中的参数（如果有）
  if (urlObj.hash) {
    const hashParams = urlObj.hash.substring(1).split('&');
    hashParams.forEach(param => {
      const [key, value] = param.split('=');
      if (key && value) {
        params[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    });
  }

  return params;
};

/**
 * 智能检测微信授权参数
 * 支持从URL参数中直接获取code和openid，适用于微信公众号消息通知场景
 * @param url 可选的URL，默认使用当前页面URL
 * @returns 授权参数检测结果
 */
export const detectWechatAuthParams = (url?: string): {
  hasCode: boolean;
  hasOpenid: boolean;
  code?: string;
  openid?: string;
  state?: string;
  source: 'url_params' | 'none';
  needsAuth: boolean;
} => {
  const targetUrl = url || window.location.href;
  const urlParams = parseWechatCallbackParams(targetUrl);

  // 检查URL参数中的授权信息
  const urlCode = urlParams.code;
  const urlOpenid = urlParams.openId;
  const urlState = urlParams.state;

  const hasCode = !!urlCode;
  const hasOpenid = !!urlOpenid;

  // 确定数据来源
  const source: 'url_params' | 'none' = (urlOpenid || urlCode) ? 'url_params' : 'none';

  // 判断是否需要重新授权
  const needsAuth = !hasOpenid && !hasCode;

  return {
    hasCode,
    hasOpenid,
    code: urlCode,
    openid: urlOpenid,
    state: urlState,
    source,
    needsAuth,
  };
};

/**
 * 清理URL中的微信授权参数
 * 用于在获取授权信息后清理URL，提升用户体验
 * @param paramsToRemove 要移除的参数名数组
 */
export const cleanWechatAuthParams = (paramsToRemove: string[] = ['code', 'state']): void => {
  try {
    const url = new URL(window.location.href);
    let hasChanges = false;

    paramsToRemove.forEach(param => {
      if (url.searchParams.has(param)) {
        url.searchParams.delete(param);
        hasChanges = true;
      }
    });

    if (hasChanges) {
      window.history.replaceState({}, document.title, url.toString());
    }
  } catch (error) {
    console.warn('清理URL参数失败:', error);
  }
};

/**
 * 验证微信授权参数的有效性
 * @param params 授权参数
 * @returns 验证结果
 */
export const validateWechatAuthParams = (params: {
  code?: string;
  openid?: string;
  state?: string;
}): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证code格式
  if (params.code) {
    if (typeof params.code !== 'string' || params.code.length < 10) {
      errors.push('授权码格式无效');
    }
  }

  // 验证openid格式
  if (params.openid) {
    if (typeof params.openid !== 'string' || params.openid.length < 20) {
      errors.push('OpenID格式无效');
    }
  }

  // 验证state参数（如果存在）
  if (params.state) {
    if (typeof params.state !== 'string') {
      warnings.push('State参数格式异常');
    }
  }

  // 检查是否至少有一个有效参数
  if (!params.code && !params.openid) {
    errors.push('缺少有效的授权参数');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * 构建微信授权URL
 * @param config 配置参数
 * @returns 授权URL
 */
export const buildWechatAuthUrl = (config: {
  appId: string;
  redirectUri: string;
  scope?: 'snsapi_base' | 'snsapi_userinfo';
  state?: string;
}): string => {
  const { appId, redirectUri, scope = 'snsapi_base', state = Date.now().toString() } = config;
  
  const params = new URLSearchParams({
    appid: appId,
    redirect_uri: redirectUri,
    response_type: 'code',
    scope,
    state,
  });
  
  return `https://open.weixin.qq.com/connect/oauth2/authorize?${params.toString()}#wechat_redirect`;
};

/**
 * 验证微信支付参数
 * @param params 支付参数
 * @returns 验证结果
 */
export const validateWechatPayParams = (params: {
  appId?: string;
  timeStamp?: string;
  nonceStr?: string;
  package?: string;
  signType?: string;
  paySign?: string;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!params.appId) {
    errors.push('缺少appId参数');
  }
  
  if (!params.timeStamp) {
    errors.push('缺少timeStamp参数');
  }
  
  if (!params.nonceStr) {
    errors.push('缺少nonceStr参数');
  }
  
  if (!params.package) {
    errors.push('缺少package参数');
  } else if (!params.package.startsWith('prepay_id=')) {
    errors.push('package参数格式错误');
  }
  
  if (!params.signType) {
    errors.push('缺少signType参数');
  } else if (!['MD5', 'HMAC-SHA256', 'RSA'].includes(params.signType)) {
    errors.push('signType参数值无效');
  }
  
  if (!params.paySign) {
    errors.push('缺少paySign参数');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * 获取微信支付错误信息
 * @param errorCode 错误码
 * @returns 错误信息
 */
export const getWechatPayErrorMessage = (errorCode: string): string => {
  const errorMessages: Record<string, string> = {
    'get_brand_wcpay_request:cancel': '用户取消支付',
    'get_brand_wcpay_request:fail': '支付失败',
    'get_brand_wcpay_request:ok': '支付成功',
    'get_brand_wcpay_request:timeout': '支付超时',
    'ORDERNOTEXIST': '订单不存在',
    'ORDERPAID': '订单已支付',
    'ORDERCLOSED': '订单已关闭',
    'SYSTEMERROR': '系统错误',
    'APPID_NOT_EXIST': 'APPID不存在',
    'MCHID_NOT_EXIST': '商户号不存在',
    'APPID_MCHID_NOT_MATCH': 'APPID和商户号不匹配',
    'LACK_PARAMS': '缺少参数',
    'OUT_TRADE_NO_USED': '商户订单号重复',
    'NOAUTH': '商户无权限',
    'AMOUNT_LIMIT': '金额超限',
    'NOT_ENOUGH': '余额不足',
  };
  
  return errorMessages[errorCode] || `支付失败: ${errorCode}`;
};

/**
 * 检查微信JS-SDK是否就绪
 * @returns Promise<boolean>
 */
export const checkWechatJSBridgeReady = (): Promise<boolean> => {
  return new Promise((resolve) => {
    if (typeof window.WeixinJSBridge !== 'undefined') {
      resolve(true);
    } else {
      if (document.addEventListener) {
        document.addEventListener('WeixinJSBridgeReady', () => resolve(true), false);
      } else if ((document as any).attachEvent) {
        (document as any).attachEvent('WeixinJSBridgeReady', () => resolve(true));
        (document as any).attachEvent('onWeixinJSBridgeReady', () => resolve(true));
      }
      
      // 设置超时
      setTimeout(() => resolve(false), 5000);
    }
  });
};

/**
 * 存储支付相关数据到本地
 * @param key 键名
 * @param data 数据
 * @param expireMinutes 过期时间（分钟）
 */
export const setPaymentStorage = (key: string, data: any, expireMinutes: number = 30): void => {
  try {
    const expireTime = Date.now() + expireMinutes * 60 * 1000;
    const storageData = {
      data,
      expireTime,
    };
    localStorage.setItem(`wechat_pay_${key}`, JSON.stringify(storageData));
  } catch (error) {
    console.error('存储支付数据失败:', error);
  }
};

/**
 * 从本地获取支付相关数据
 * @param key 键名
 * @returns 数据或null
 */
export const getPaymentStorage = (key: string): any => {
  try {
    const stored = localStorage.getItem(`wechat_pay_${key}`);
    if (!stored) {
      return null;
    }
    
    const storageData = JSON.parse(stored);
    if (Date.now() > storageData.expireTime) {
      localStorage.removeItem(`wechat_pay_${key}`);
      return null;
    }
    
    return storageData.data;
  } catch (error) {
    console.error('获取支付数据失败:', error);
    return null;
  }
};

/**
 * 清理过期的支付存储数据
 * 注意：已移除微信授权数据的清理逻辑
 */
export const cleanExpiredPaymentStorage = (): void => {
  try {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('wechat_pay_')) {
        const stored = localStorage.getItem(key);
        if (stored) {
          try {
            const storageData = JSON.parse(stored);
            if (Date.now() > storageData.expireTime) {
              localStorage.removeItem(key);
            }
          } catch {
            // 如果解析失败，直接删除
            localStorage.removeItem(key);
          }
        }
      }
    });
  } catch (error) {
    console.error('清理过期支付数据失败:', error);
  }
};

/**
 * 安全清理微信支付相关数据
 * 注意：已移除微信授权数据的清理逻辑，只清理支付相关数据
 */
export const secureCleanWechatPayData = (): void => {
  try {
    // 清理localStorage中的支付数据
    const localKeys = Object.keys(localStorage);
    localKeys.forEach(key => {
      if (key.includes('wechat_pay_') || key.includes('pay')) {
        localStorage.removeItem(key);
      }
    });

    // 清理sessionStorage中的支付数据
    const sessionKeys = Object.keys(sessionStorage);
    sessionKeys.forEach(key => {
      if (key.includes('wechat_pay_') || key.includes('pay')) {
        sessionStorage.removeItem(key);
      }
    });

    console.log('已安全清理微信支付相关数据');
  } catch (error) {
    console.error('清理微信支付数据失败:', error);
  }
};
